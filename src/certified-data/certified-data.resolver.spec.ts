import { Test, TestingModule } from '@nestjs/testing'
import { CertifiedDataResolver } from './certified-data.resolver'
import { CertifiedDataService } from './certified-data.service'

describe('CertifiedDataResolver', () => {
    let resolver: CertifiedDataResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [CertifiedDataResolver, CertifiedDataService],
        }).compile()

        resolver = module.get<CertifiedDataResolver>(CertifiedDataResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

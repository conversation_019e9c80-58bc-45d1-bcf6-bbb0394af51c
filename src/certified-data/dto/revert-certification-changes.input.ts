import { ObjectType, Field, Int } from '@nestjs/graphql'
import {
    IsString,
    IsBoolean,
    IsInt,
    IsArray,
    IsOptional,
    Min,
    ArrayNotEmpty,
    IsUUID,
} from 'class-validator'

@ObjectType()
export class RevertedEntityInfo {
    @Field()
    @IsString()
    @IsUUID('4')
    certifiedDataId: string

    @Field()
    @IsString()
    entityType: string

    @Field()
    @IsString()
    @IsUUID('4')
    entityId: string

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    approvedChangesCleared: string[]

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    requestedChangesCleared: string[]
}

@ObjectType()
export class RevertChangesResponse {
    @Field()
    @IsBoolean()
    success: boolean

    @Field(() => Int)
    @IsInt()
    @Min(1900)
    certificationYear: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalCertifiedDataRecords: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalEntitiesReverted: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalRejectionReasonsDeleted: number

    @Field()
    @IsString()
    message: string

    @Field(() => [RevertedEntityInfo], { nullable: true })
    @IsOptional()
    @IsArray()
    revertedEntities?: RevertedEntityInfo[]
}

@ObjectType()
export class RevertChangesPreviewItem {
    @Field()
    @IsString()
    @IsUUID('4')
    certifiedDataId: string

    @Field()
    @IsString()
    participantName: string

    @Field()
    @IsString()
    entityType: string

    @Field()
    @IsString()
    @IsUUID('4')
    entityId: string

    @Field(() => Int)
    @IsInt()
    @Min(0)
    approvedChangesCount: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    requestedChangesCount: number

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    approvedChanges: string[]

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    requestedChanges: string[]
}

@ObjectType()
export class RevertChangesPreview {
    @Field(() => Int)
    @IsInt()
    @Min(1900)
    certificationYear: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalCertifiedDataRecords: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalEntitiesAffected: number

    @Field(() => Int)
    @IsInt()
    @Min(0)
    totalRejectionReasons: number

    @Field(() => [RevertChangesPreviewItem])
    @IsArray()
    @ArrayNotEmpty()
    affectedEntities: RevertChangesPreviewItem[]

    @Field()
    @IsString()
    estimatedImpact: string
}

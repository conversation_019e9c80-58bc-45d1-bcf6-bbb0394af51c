import { InputType, Field, Float } from '@nestjs/graphql'
import { IsNumber, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedPensionCorrectionsStartOfYearInput {
    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    attainableGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualPartnersPension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualSinglesPension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    grossAnnualDisabilityPension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualPartnersPension?: number

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}

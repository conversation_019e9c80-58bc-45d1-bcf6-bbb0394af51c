import { InputType, Field, Int } from '@nestjs/graphql'
import { IsInt, IsOptional, IsString, Min, Max } from 'class-validator'

@InputType()
export class GetCertificationParticipantsInput {
    @Field(() => Int)
    @IsInt()
    @Min(2020)
    @Max(2100)
    year: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    skip?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    take?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    department?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    searchQuery?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortBy?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortOrder?: 'asc' | 'desc'
}

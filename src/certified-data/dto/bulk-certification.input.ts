import { Field, InputType, ObjectType, Int } from '@nestjs/graphql'
import { IsString, IsArray, IsNumber, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class BulkStartCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    participantIds: string[]

    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class BulkApproveCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    certificationIds: string[]
}

@InputType()
export class BulkRejectCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    certificationIds: string[]

    @Field()
    @IsString()
    reason: string
}

@InputType()
export class GetCertificationStatsInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class GetAutoApproveEligibleInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class GetCommonChangePatternsInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@ObjectType()
export class BulkOperationFailure {
    @Field()
    @IsOptional()
    @IsString()
    participantId?: string

    @Field()
    @IsOptional()
    @IsString()
    certificationId?: string

    @Field()
    @IsString()
    reason: string
}

@ObjectType()
export class BulkStartCertificationResponse {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    successful: string[]

    @Field(() => [BulkOperationFailure])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkOperationFailure)
    failed: BulkOperationFailure[]
}

@ObjectType()
export class BulkApproveCertificationResponse {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    successful: string[]

    @Field(() => [BulkOperationFailure])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkOperationFailure)
    failed: BulkOperationFailure[]
}

@ObjectType()
export class BulkRejectCertificationResponse {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    successful: string[]

    @Field(() => [BulkOperationFailure])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => BulkOperationFailure)
    failed: BulkOperationFailure[]
}

@ObjectType()
export class CertificationStats {
    @Field(() => Int)
    @IsNumber()
    totalParticipants: number

    @Field(() => Int)
    @IsNumber()
    pendingCertifications: number

    @Field(() => Int)
    @IsNumber()
    inProgress: number

    @Field(() => Int)
    @IsNumber()
    completed: number

    @Field(() => Int)
    @IsNumber()
    requiresAttention: number
}

@ObjectType()
export class AutoApproveEligible {
    @Field()
    @IsString()
    certificationId: string

    @Field()
    @IsString()
    participantId: string

    @Field()
    @IsString()
    participantName: string

    @Field(() => Int)
    @IsNumber()
    changes: number
}

@ObjectType()
export class CommonChangePattern {
    @Field()
    @IsString()
    field: string

    @Field(() => Int)
    @IsNumber()
    count: number

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    certificationIds: string[]
}

import { InputType, Field, GraphQLISODateTime } from '@nestjs/graphql'
import {
    IsBoolean,
    IsDate,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedPartnerInfoInput {
    @Field()
    @IsBoolean()
    isCurrent: boolean

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    firstName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    lastName?: string

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDate()
    @IsOptional()
    @Type(() => Date)
    dateOfBirth?: Date

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isDeceased?: boolean = false

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDate()
    @IsOptional()
    @Type(() => Date)
    startDate?: Date

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}

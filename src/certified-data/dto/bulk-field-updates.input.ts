import { Field, InputType, Int, ObjectType } from '@nestjs/graphql'
import { IsArray, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class ApproveFieldsOperation {
  @Field(() => String)
  @IsString()
  certifiedDataId: string

  @Field(() => String)
  @IsString()
  entityType: string

  @Field(() => [String])
  @IsArray()
  fields: string[]

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  entityId?: string
}

@InputType()
export class RejectFieldsOperation {
  @Field(() => String)
  @IsString()
  certifiedDataId: string

  @Field(() => String)
  @IsString()
  entityType: string

  @Field(() => [String])
  @IsArray()
  fields: string[]

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  entityId?: string
}

@InputType()
export class BulkApproveFieldsInput {
  @Field(() => [ApproveFieldsOperation])
  @ValidateNested({ each: true })
  @Type(() => ApproveFieldsOperation)
  operations: ApproveFieldsOperation[]
}

@InputType()
export class BulkRejectFieldsInput {
  @Field(() => [RejectFieldsOperation])
  @ValidateNested({ each: true })
  @Type(() => RejectFieldsOperation)
  operations: RejectFieldsOperation[]

  @Field(() => String)
  @IsNotEmpty()
  @IsString()
  rejectReason: string
}

@ObjectType()
export class BulkFieldUpdateFailure {
  @Field(() => String)
  certifiedDataId: string

  @Field(() => String)
  entityType: string

  @Field(() => String)
  message: string
}

@ObjectType()
export class BulkFieldUpdateResponse {
  @Field(() => Int)
  successCount: number

  @Field(() => Int)
  failureCount: number

  @Field(() => [BulkFieldUpdateFailure])
  failed: BulkFieldUpdateFailure[]
}


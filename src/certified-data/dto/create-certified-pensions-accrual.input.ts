import { InputType, Field, Float } from '@nestjs/graphql'
import { IsNumber, IsOptional, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedPensionsAccrualInput {
    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    pensionBase?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    opteAccrualToReferenceDate?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    wpteAccrualToReferenceDate?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    opteAccrualAfterReferenceDate?: number

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}

import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertifiedDataInput } from './dto'
import { FindAllCertifiedDataInput } from './dto'
import { FindOneCertifiedDataInput } from './dto'
import { AuditLogService } from '../audit-log/audit-log.service'
import { NotificationService } from '../notifications/notifications.service'
import {
    RevertSingleFieldInput,
    RevertSingleFieldResponse,
} from './dto/revert-single-field.input'
import { CertificationRejectReasonService } from '../certification-reject-reason/certification-reject-reason.service'

@Injectable()
export class CertifiedDataService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly auditLogService: AuditLogService,
        private readonly notificationService: NotificationService,
        private readonly certificationRejectReasonService: CertificationRejectReasonService
    ) {}

    private getCertifiedDataIncludes() {
        return {
            participant: true,
            certifiedBy: true,
            certifiedPensionInfo: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedEmploymentInfo: {
                include: {
                    certifiedSalaryEntries: {
                        include: {
                            certificationRejectReason: true,
                        },
                    },
                    certificationRejectReason: true,
                },
            },
            certifiedPersonalInfo: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedIndexationStartOfYear: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedPensionCorrections: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedPensionCorrectionsStartOfYear: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedPensionCorrectionsEndOfYear: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedVoluntaryContributions: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedAddress: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedChild: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedPartnerInfo: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certifiedPensionParameters: {
                include: {
                    certificationRejectReason: true,
                },
            },
            certificationRejectReason: true,
        }
    }

    async create(
        createCertifiedDataInput: CreateCertifiedDataInput,
        participantId: string,
        certifiedById: string,
        userId: string
    ) {
        // Check if participant exists
        const participant = await this.prisma.participant.findUnique({
            where: { id: participantId },
        })

        if (!participant) {
            throw new NotFoundException(
                `Participant with ID "${participantId}" not found`
            )
        }

        // Check if certification for this year already exists
        const existingCertification =
            await this.prisma.certifiedData.findUnique({
                where: {
                    participantId_certificationYear: {
                        participantId: participantId,
                        certificationYear:
                            createCertifiedDataInput.certificationYear,
                    },
                },
            })

        if (existingCertification) {
            throw new BadRequestException(
                `Certification for participant ${participantId} and year ${createCertifiedDataInput.certificationYear} already exists`
            )
        }

        // Set certifiedAt to current date if not provided
        if (!createCertifiedDataInput.certifiedAt) {
            createCertifiedDataInput.certifiedAt = new Date()
        }

        // Extract nested data objects
        const {
            certifiedPensionInfo,
            certifiedEmploymentInfo,
            certifiedPersonalInfo,
            certifiedIndexationStartOfYear,
            certifiedPensionCorrections,
            certifiedVoluntaryContributions,
            certifiedPensionParameters,
            certificationRejectReason,
            participant: participantInput,
            certifiedBy: certifiedByInput,
            ...certifiedDataInput
        } = createCertifiedDataInput

        // Create the certified data and its relations in a transaction
        const certifiedData = await this.prisma.$transaction(async (prisma) => {
            // Create the main certified data entry
            const certifiedData = await prisma.certifiedData.create({
                data: {
                    ...certifiedDataInput,
                    participant: { connect: { id: participantId } },
                    certifiedBy: { connect: { id: certifiedById } },
                },
                include: this.getCertifiedDataIncludes(),
            })

            // Create certificationRejectReason if provided
            if (
                certificationRejectReason &&
                certificationRejectReason.length > 0
            ) {
                await Promise.all(
                    certificationRejectReason.map((reason) =>
                        prisma.certificationRejectReason.create({
                            data: {
                                field: reason.field,
                                reason: reason.reason,
                                certifiedData: {
                                    connect: { id: certifiedData.id },
                                },
                            },
                        })
                    )
                )
            }

            // Create related models if provided
            if (certifiedPensionInfo) {
                const {
                    certificationRejectReason: pensionRejectReason,
                    ...pensionData
                } = certifiedPensionInfo
                const createdPensionInfo =
                    await prisma.certifiedPensionInfo.create({
                        data: {
                            ...pensionData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for pension info if provided
                if (pensionRejectReason && pensionRejectReason.length > 0) {
                    await Promise.all(
                        pensionRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedPensionInfo: {
                                        connect: { id: createdPensionInfo.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedEmploymentInfo) {
                const {
                    certificationRejectReason: employmentRejectReason,
                    ...employmentData
                } = certifiedEmploymentInfo
                const createdEmploymentInfo =
                    await prisma.certifiedEmploymentInfo.create({
                        data: {
                            ...employmentData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for employment info if provided
                if (
                    employmentRejectReason &&
                    employmentRejectReason.length > 0
                ) {
                    await Promise.all(
                        employmentRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedEmploymentInfo: {
                                        connect: {
                                            id: createdEmploymentInfo.id,
                                        },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedPersonalInfo) {
                const {
                    certificationRejectReason: personalRejectReason,
                    ...personalData
                } = certifiedPersonalInfo
                const createdPersonalInfo =
                    await prisma.certifiedPersonalInfo.create({
                        data: {
                            ...personalData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for personal info if provided
                if (personalRejectReason && personalRejectReason.length > 0) {
                    await Promise.all(
                        personalRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedPersonalInfo: {
                                        connect: { id: createdPersonalInfo.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedIndexationStartOfYear) {
                const {
                    certificationRejectReason: indexationRejectReason,
                    ...indexationData
                } = certifiedIndexationStartOfYear
                const createdIndexation =
                    await prisma.certifiedIndexationStartOfYear.create({
                        data: {
                            ...indexationData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for indexation if provided
                if (
                    indexationRejectReason &&
                    indexationRejectReason.length > 0
                ) {
                    await Promise.all(
                        indexationRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedIndexationStartOfYear: {
                                        connect: { id: createdIndexation.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedPensionCorrections) {
                const {
                    certificationRejectReason: correctionsRejectReason,
                    ...correctionsData
                } = certifiedPensionCorrections
                const createdCorrections =
                    await prisma.certifiedPensionCorrections.create({
                        data: {
                            ...correctionsData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for corrections if provided
                if (
                    correctionsRejectReason &&
                    correctionsRejectReason.length > 0
                ) {
                    await Promise.all(
                        correctionsRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedPensionCorrections: {
                                        connect: { id: createdCorrections.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedVoluntaryContributions) {
                const {
                    certificationRejectReason: voluntaryRejectReason,
                    ...voluntaryData
                } = certifiedVoluntaryContributions
                const createdVoluntary =
                    await prisma.certifiedVoluntaryContributions.create({
                        data: {
                            ...voluntaryData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for voluntary contributions if provided
                if (voluntaryRejectReason && voluntaryRejectReason.length > 0) {
                    await Promise.all(
                        voluntaryRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedVoluntaryContributions: {
                                        connect: { id: createdVoluntary.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            if (certifiedPensionParameters) {
                const {
                    certificationRejectReason: parametersRejectReason,
                    ...parametersData
                } = certifiedPensionParameters
                const createdParameters =
                    await prisma.certifiedPensionParameters.create({
                        data: {
                            ...parametersData,
                            certifiedData: {
                                connect: { id: certifiedData.id },
                            },
                        },
                    })

                // Create certificationRejectReason for parameters if provided
                if (
                    parametersRejectReason &&
                    parametersRejectReason.length > 0
                ) {
                    await Promise.all(
                        parametersRejectReason.map((reason) =>
                            prisma.certificationRejectReason.create({
                                data: {
                                    field: reason.field,
                                    reason: reason.reason,
                                    certifiedPensionParameters: {
                                        connect: { id: createdParameters.id },
                                    },
                                },
                            })
                        )
                    )
                }
            }

            // Fetch the complete record with all relations
            return prisma.certifiedData.findUnique({
                where: { id: certifiedData.id },
                include: this.getCertifiedDataIncludes(),
            })
        })

        await this.auditLogService.create({
            action: 'CREATE_CERTIFICATION',
            userId,
            entityId: certifiedData.id,
            entityType: 'CERTIFIED_DATA',
            changes: createCertifiedDataInput,
        })

        return certifiedData
    }

    async findAll(findAllCertifiedDataInput: FindAllCertifiedDataInput) {
        const {
            participantId,
            certificationYear,
            skip,
            take,
            sortBy,
            sortOrder,
        } = findAllCertifiedDataInput

        const where: any = {}

        if (participantId) {
            where.participantId = participantId
        }

        if (certificationYear) {
            where.certificationYear = certificationYear
        }

        const orderBy: any = {}
        if (sortBy) {
            orderBy[sortBy] = sortOrder || 'desc'
        } else {
            orderBy.certificationYear = 'desc'
        }

        const certifiedDataItems = await this.prisma.certifiedData.findMany({
            where,
            orderBy,
            skip: skip || 0,
            take: take || 50,
            include: this.getCertifiedDataIncludes(),
        })

        const totalCount = await this.prisma.certifiedData.count({ where })

        return {
            items: certifiedDataItems,
            totalCount,
        }
    }

    async findOne(findOneCertifiedDataInput: FindOneCertifiedDataInput) {
        const { id, participantId, certificationYear } =
            findOneCertifiedDataInput

        let certifiedData

        if (id) {
            certifiedData = await this.prisma.certifiedData.findUnique({
                where: { id },
                include: this.getCertifiedDataIncludes(),
            })
        } else if (participantId && certificationYear) {
            certifiedData = await this.prisma.certifiedData.findUnique({
                where: {
                    participantId_certificationYear: {
                        participantId,
                        certificationYear,
                    },
                },
                include: this.getCertifiedDataIncludes(),
            })
        } else {
            throw new BadRequestException(
                'Either id or (participantId and certificationYear) must be provided'
            )
        }

        if (!certifiedData) {
            throw new NotFoundException('Certified data not found')
        }

        return certifiedData
    }

    async findByParticipant(participantId: string) {
        const currentYear = new Date().getFullYear()
        // const targetYears = [currentYear, currentYear - 1, currentYear - 2]

        return this.prisma.certifiedData.findMany({
            where: {
                participantId,
                // certificationYear: {
                //     in: targetYears,
                // },
            },
            orderBy: {
                certificationYear: 'desc',
            },
            include: this.getCertifiedDataIncludes(),
        })
    }
    // Get all certifications for a specific year
    async findByYear(certificationYear: number) {
        return this.prisma.certifiedData.findMany({
            where: {
                certificationYear,
            },
            orderBy: {
                participantId: 'asc',
            },
            include: this.getCertifiedDataIncludes(),
        })
    }

    // Get the latest certification for a participant
    async findLatestForParticipant(participantId: string) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                participantId,
            },
            orderBy: {
                certificationYear: 'desc',
            },
            take: 1,
            include: this.getCertifiedDataIncludes(),
        })

        if (certifications.length === 0) {
            return null
        }

        return certifications[0]
    }

    //update status
    async updateStatus(id: string, status: string, userId: string) {
        const certifiedData = await this.prisma.certifiedData.update({
            where: { id },
            data: {
                certificationStatus: status,
            },
            include: this.getCertifiedDataIncludes(),
        })

        return certifiedData
    }

    async findCertifiedDataByYearAndYearBefore(certificationYear: number) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: {
                    in: [certificationYear, certificationYear - 1],
                },
            },
            orderBy: [{ certificationYear: 'desc' }, { participantId: 'asc' }],
            include: this.getCertifiedDataIncludes(),
        })

        // Group the certifications by year
        const groupedByYear = certifications.reduce(
            (acc, certification) => {
                const yearKey = certification.certificationYear.toString()
                if (!acc[yearKey]) {
                    acc[yearKey] = []
                }
                acc[yearKey].push(certification)
                return acc
            },
            {} as Record<string, typeof certifications>
        )

        // Add differences to each nested object if we have both years
        const result: any = { ...groupedByYear }

        if (result[certificationYear] && result[certificationYear - 1]) {
            // For each participant (assuming same participants exist in both years)
            const currentYearParticipants = result[certificationYear]
            const previousYearParticipants = result[certificationYear - 1]

            currentYearParticipants.forEach((current: any, index: number) => {
                const previous = previousYearParticipants[index]
                if (current && previous) {
                    this.markDifferences(current, previous)
                }
            })
        }

        return { data: result }
    }

    private markDifferences(current: any, previous: any) {
        const nestedObjects = [
            'certifiedPensionInfo',
            'certifiedEmploymentInfo',
            'certifiedPersonalInfo',
            'certifiedAddress',
            'certifiedIndexationStartOfYear',
            'certifiedPensionCorrections',
            'certifiedVoluntaryContributions',
            'certifiedPensionParameters',
        ]

        const arrayFields = ['certifiedChild', 'certifiedPartnerInfo']

        current.differences = this.findTopLevelDifferences(current, previous)

        // Handle nested objects
        nestedObjects.forEach((nestedField) => {
            // Check if either current or previous exists (not both null)
            if (current[nestedField] || previous[nestedField]) {
                // If one is null and the other isn't, that's a difference
                if (!current[nestedField] || !previous[nestedField]) {
                    current[nestedField] = current[nestedField] || {}
                    current[nestedField].differences = ['_exists']
                } else {
                    current[nestedField].differences =
                        this.findNestedDifferences(
                            current[nestedField],
                            previous[nestedField]
                        )
                }
            }
        })

        // Handle array fields
        arrayFields.forEach((arrayField) => {
            const differencesFieldName = `${arrayField}Differences`

            if (current[arrayField] || previous[arrayField]) {
                const currentArray = current[arrayField] || []
                const previousArray = previous[arrayField] || []

                // Compare array contents
                const arrayDifferences = this.findArrayDifferences(
                    currentArray,
                    previousArray
                )
                if (arrayDifferences.length > 0) {
                    current[differencesFieldName] = arrayDifferences
                }
            }
        })

        if (
            current.certifiedEmploymentInfo?.certifiedSalaryEntries &&
            previous.certifiedEmploymentInfo?.certifiedSalaryEntries
        ) {
            current.certifiedEmploymentInfo.certifiedSalaryEntriesDifferences =
                this.findSalaryEntriesDifferences(
                    current.certifiedEmploymentInfo.certifiedSalaryEntries,
                    previous.certifiedEmploymentInfo.certifiedSalaryEntries
                )
        }
    }

    private findTopLevelDifferences(current: any, previous: any): string[] {
        const differences: string[] = []
        const fieldsToCompare = ['certificationStatus', 'notes']

        fieldsToCompare.forEach((field) => {
            if (!this.isEqual(current[field], previous[field])) {
                differences.push(field)
            }
        })

        return differences
    }

    private findNestedDifferences(current: any, previous: any): string[] {
        const differences: string[] = []

        for (const key in current) {
            if (
                key === 'id' ||
                key === 'certifiedDataId' ||
                key === 'pendingChanges'
            )
                continue

            if (
                typeof current[key] === 'object' &&
                current[key] !== null &&
                !Array.isArray(current[key])
            ) {
                // Skip nested object comparison here (handled separately)
                continue
            } else if (!this.isEqual(current[key], previous[key])) {
                differences.push(key)
            }
        }

        return differences
    }

    private findSalaryEntriesDifferences(
        currentEntries: any[],
        previousEntries: any[]
    ): any {
        const differences: any[] = []
        const entriesByYearCurrent = this.groupByYear(currentEntries)
        const entriesByYearPrevious = this.groupByYear(previousEntries)

        for (const year in entriesByYearCurrent) {
            if (entriesByYearPrevious[year]) {
                const currentEntry = entriesByYearCurrent[year][0]
                const previousEntry = entriesByYearPrevious[year][0]
                const entryDifferences: string[] = []

                for (const key in currentEntry) {
                    if (
                        key === 'id' ||
                        key === 'certifiedEmploymentInfoId' ||
                        key === 'pendingChanges'
                    )
                        continue

                    if (!this.isEqual(currentEntry[key], previousEntry[key])) {
                        entryDifferences.push(key)
                    }
                }

                if (entryDifferences.length > 0) {
                    differences.push({
                        year: year,
                        fields: entryDifferences,
                    })
                }
            }
        }

        return differences
    }

    private groupByYear(entries: any[]): Record<string, any[]> {
        return entries.reduce((acc, entry) => {
            const year = entry.year
            if (!acc[year]) acc[year] = []
            acc[year].push(entry)
            return acc
        }, {})
    }

    private isEqual(value1: any, value2: any): boolean {
        if (value1 instanceof Date && value2 instanceof Date) {
            return value1.getTime() === value2.getTime()
        }
        return JSON.stringify(value1) === JSON.stringify(value2)
    }

    private findArrayDifferences(
        currentArray: any[],
        previousArray: any[]
    ): string[] {
        const differences: string[] = []

        // Compare items that exist in both arrays
        const minLength = Math.min(currentArray.length, previousArray.length)
        for (let i = 0; i < minLength; i++) {
            const currentItem = currentArray[i]
            const previousItem = previousArray[i]

            // Compare all fields except id and foreign keys
            for (const key in currentItem) {
                if (
                    key === 'id' ||
                    key === 'certifiedDataId' ||
                    key === 'pendingChanges' ||
                    key === 'requestedChanges' ||
                    key === 'approvedChanges' ||
                    key === 'certificationRejectReason'
                )
                    continue

                if (!this.isEqual(currentItem[key], previousItem[key])) {
                    // Return index and field that differs
                    differences.push(`[${i}].${key}`)
                }
            }
        }

        // For items that only exist in current array, mark all their fields as different
        for (let i = minLength; i < currentArray.length; i++) {
            const currentItem = currentArray[i]

            // Add all fields of the new item as differences
            for (const key in currentItem) {
                if (
                    key === 'id' ||
                    key === 'certifiedDataId' ||
                    key === 'pendingChanges' ||
                    key === 'requestedChanges' ||
                    key === 'approvedChanges' ||
                    key === 'certificationRejectReason'
                )
                    continue

                differences.push(`[${i}].${key}`)
            }
        }

        return differences
    }

    async updateApprovedChanges(
        id: string,
        approvedChanges: string[],
        entityType: string = 'certifiedData',
        entityId: string = ''
    ) {
        const certifiedData = await this.prisma.certifiedData.findUnique({
            where: { id },
            include: this.getCertifiedDataIncludes(),
        })

        if (!certifiedData) {
            throw new NotFoundException(`CertifiedData with ID ${id} not found`)
        }

        const entityModelMap: Record<string, string> = {
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
            certifiedAddress: 'certifiedAddress',
            certifiedChild: 'certifiedChild',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedSalaryEntry: 'certifiedSalaryEntry',
        }

        // Get the Prisma model name
        const modelName = entityModelMap[entityType]
        if (!modelName) {
            throw new BadRequestException(`Invalid entity type: ${entityType}`)
        }

        // For related entities
        const relatedEntity = certifiedData[entityType]
        if (!relatedEntity) {
            throw new NotFoundException(
                `${entityType} not found for CertifiedData with ID ${id}`
            )
        }

        // Handle array relationships (certifiedPartnerInfo and certifiedChildren)
        let entityToUpdate
        if (Array.isArray(relatedEntity)) {
            if (!entityId) {
                throw new BadRequestException(
                    `entityId is required for array relationships like ${entityType}`
                )
            }

            // Find the specific entity by ID
            entityToUpdate = relatedEntity.find(
                (entity) => entity.id === entityId
            )

            if (!entityToUpdate) {
                throw new NotFoundException(
                    `${entityType} with ID ${entityId} not found in CertifiedData ${id}`
                )
            }
        } else {
            entityToUpdate = relatedEntity
        }

        // Get current approvedChanges and requestedChanges
        const currentApprovedChanges = entityToUpdate.approvedChanges || []
        const currentRequestedChanges = entityToUpdate.requestedChanges || []

        // Add new approved changes and remove them from requested changes
        const uniqueApprovedChanges = [
            ...new Set([...currentApprovedChanges, ...approvedChanges]),
        ]
        const updatedRequestedChanges = currentRequestedChanges.filter(
            (change) => !approvedChanges.includes(change)
        )

        // Update the entity with both arrays
        await this.prisma[modelName].update({
            where: { id: entityToUpdate.id },
            data: {
                approvedChanges: uniqueApprovedChanges,
                requestedChanges: updatedRequestedChanges,
            },
        })

        // Return the updated certified data with all relations
        return this.prisma.certifiedData.findUnique({
            where: { id },
            include: this.getCertifiedDataIncludes(),
        })
    }

    async updateRejectedChanges(
        id: string,
        rejectedChanges: string[],
        entityType: string = 'certifiedData',
        rejectReason: string = '',
        userId: string,
        entityId: string = ''
    ) {
        console.log({ userId })
        // First check if certified data exists
        const certifiedData = await this.prisma.certifiedData.findUnique({
            where: { id },
            include: this.getCertifiedDataIncludes(),
        })

        const entityTypeSmall =
            entityType.charAt(0).toLowerCase() + entityType.slice(1)

        if (!certifiedData) {
            throw new NotFoundException(`CertifiedData with ID ${id} not found`)
        }

        // Map of entity types to their Prisma model names
        const entityModelMap: Record<string, string> = {
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedSalaryEntry: 'certifiedSalaryEntry',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
            certifiedChild: 'certifiedChild',
            certifiedAddress: 'certifiedAddress',
        }

        // Get the Prisma model name
        const modelName = entityModelMap[entityTypeSmall]
        if (!modelName) {
            throw new BadRequestException(
                `Invalid entity type: ${entityTypeSmall}`
            )
        }

        // For related entities
        const relatedEntity = certifiedData[entityTypeSmall]
        if (!relatedEntity) {
            throw new NotFoundException(
                `${entityTypeSmall} not found for CertifiedData with ID ${id}`
            )
        }

        // Handle array relationships (certifiedPartnerInfo and certifiedChildren)
        let entityToUpdate
        if (Array.isArray(relatedEntity)) {
            if (!entityId) {
                throw new BadRequestException(
                    `entityId is required for array relationships like ${entityTypeSmall}`
                )
            }

            // Find the specific entity by ID
            entityToUpdate = relatedEntity.find(
                (entity) => entity.id === entityId
            )

            if (!entityToUpdate) {
                throw new NotFoundException(
                    `${entityTypeSmall} with ID ${entityId} not found in CertifiedData ${id}`
                )
            }
        } else {
            entityToUpdate = relatedEntity
        }

        // Get current requestedChanges and approvedChanges
        const currentRequestedChanges = entityToUpdate.requestedChanges || []
        const currentApprovedChanges = entityToUpdate.approvedChanges || []

        // Add new rejected changes to requestedChanges and remove them from approvedChanges
        const uniqueRequestedChanges = [
            ...new Set([...currentRequestedChanges, ...rejectedChanges]),
        ]
        const updatedApprovedChanges = currentApprovedChanges.filter(
            (change) => !rejectedChanges.includes(change)
        )

        // Update the related entity and create CertificationRejectReason records in a transaction
        await this.prisma.$transaction(async (prisma) => {
            // Update the related entity
            await prisma[modelName].update({
                where: { id: entityToUpdate.id },
                data: {
                    requestedChanges: uniqueRequestedChanges,
                    approvedChanges: updatedApprovedChanges,
                },
            })

            // If rejectReason is provided, create CertificationRejectReason records
            if (rejectReason && rejectedChanges.length > 0) {
                // Prepare the foreign key field based on entity type
                const foreignKeyMap: Record<string, string> = {
                    certifiedData: 'certifiedDataId',
                    certifiedPensionInfo: 'certifiedPensionInfoId',
                    certifiedEmploymentInfo: 'certifiedEmploymentInfoId',
                    certifiedPersonalInfo: 'certifiedPersonalInfoId',
                    certifiedIndexationStartOfYear:
                        'certifiedIndexationStartOfYearId',
                    certifiedPensionCorrections:
                        'certifiedPensionCorrectionsId',
                    certifiedVoluntaryContributions:
                        'certifiedVoluntaryContributionsId',
                    certifiedPensionParameters: 'certifiedPensionParametersId',
                    certifiedSalaryEntry: 'certifiedSalaryEntryId',
                    certifiedPartnerInfo: 'certifiedPartnerInfoId',
                    certifiedChild: 'certifiedChildId',
                    certifiedAddress: 'certifiedAddressId',
                }

                const foreignKeyField = foreignKeyMap[entityTypeSmall]
                if (!foreignKeyField) {
                    throw new BadRequestException(
                        `Invalid entity type for foreign key mapping: ${entityTypeSmall}`
                    )
                }

                // Create a CertificationRejectReason for each rejected field
                await Promise.all(
                    rejectedChanges.map((field) =>
                        prisma.certificationRejectReason.create({
                            data: {
                                field,
                                reason: rejectReason,
                                status: 'VALID', // Default to VALID status
                                [foreignKeyField]: entityToUpdate.id,
                            },
                        })
                    )
                )
            }
        })

        const fieldNames = rejectedChanges.join(', ')
        const entityDisplayName = entityTypeSmall
            .replace(/([A-Z])/g, ' $1')
            .trim()
        const message = `Your ${entityDisplayName} field: ${fieldNames} has been rejected for certification year ${
            certifiedData.certificationYear
        }.${rejectReason ? ` Reason: ${rejectReason}` : ''}`

        const notification = await this.notificationService.create(
            {
                message,
                recipientId: userId,
                type: 'CERTIFICATION_REJECTION',
                entityId: certifiedData.id,
                entityType: 'CERTIFIED_DATA',
            },
            userId
        )

        return this.prisma.certifiedData.findUnique({
            where: { id },
            include: this.getCertifiedDataIncludes(),
        })
    }

    async revertApprovedRejectedChanges(
        certificationYear: number,
        userId: string
    ) {
        const certifiedDataList = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: certificationYear,
            },
            include: this.getCertifiedDataIncludes(),
        })

        if (certifiedDataList.length === 0) {
            throw new NotFoundException(
                `No certified data found for year ${certificationYear}`
            )
        }

        // Define all certified entity types that have approvedChanges/requestedChanges
        const certifiedEntityTypes = [
            'certifiedData',
            'certifiedPensionInfo',
            'certifiedEmploymentInfo',
            'certifiedPersonalInfo',
            'certifiedIndexationStartOfYear',
            'certifiedPensionCorrections',
            'certifiedVoluntaryContributions',
            'certifiedPensionParameters',
            'certifiedAddress',
            'certifiedChild',
            'certifiedPartnerInfo',
        ]

        // Map of entity types to their Prisma model names
        const entityModelMap: Record<string, string> = {
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedAddress: 'certifiedAddress',
            certifiedChild: 'certifiedChild',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
        }

        // Track statistics for the revert operation
        let totalEntitiesReverted = 0
        let totalRejectionReasonsDeleted = 0
        const revertedEntities: Array<{
            certifiedDataId: string
            entityType: string
            entityId: string
            changes: {
                approvedChangesCleared: string[]
                requestedChangesCleared: string[]
            }
        }> = []

        // Process each certified data record
        await this.prisma.$transaction(async (prisma) => {
            for (const certifiedData of certifiedDataList) {
                // Process each entity type
                for (const entityType of certifiedEntityTypes) {
                    const modelName = entityModelMap[entityType]
                    if (!modelName) continue

                    const relatedEntity = certifiedData[entityType]
                    if (!relatedEntity) continue

                    if (Array.isArray(relatedEntity)) {
                        for (const entity of relatedEntity) {
                            await this.revertSingleEntity(
                                prisma,
                                modelName,
                                entity,
                                certifiedData.id,
                                entityType,
                                revertedEntities
                            )
                            totalEntitiesReverted++
                        }
                    } else {
                        // Handle single relationships
                        await this.revertSingleEntity(
                            prisma,
                            modelName,
                            relatedEntity,
                            certifiedData.id,
                            entityType,
                            revertedEntities
                        )
                        totalEntitiesReverted++
                    }
                }

                // Handle special case for certifiedSalaryEntries (nested under certifiedEmploymentInfo)
                if (
                    certifiedData.certifiedEmploymentInfo
                        ?.certifiedSalaryEntries
                ) {
                    for (const salaryEntry of certifiedData
                        .certifiedEmploymentInfo.certifiedSalaryEntries) {
                        await this.revertSingleEntity(
                            prisma,
                            'certifiedSalaryEntry',
                            salaryEntry,
                            certifiedData.id,
                            'certifiedSalaryEntry',
                            revertedEntities
                        )
                        totalEntitiesReverted++
                    }
                }
                // Build OR conditions for deletion
                const deleteConditions = []

                // Add main certified data condition
                deleteConditions.push({ certifiedDataId: certifiedData.id })

                // Add conditions for each related entity
                certifiedEntityTypes.forEach((entityType) => {
                    const relatedEntity = certifiedData[entityType]
                    if (!relatedEntity) return

                    const foreignKeyMap: Record<string, string> = {
                        certifiedPensionInfo: 'certifiedPensionInfoId',
                        certifiedEmploymentInfo: 'certifiedEmploymentInfoId',
                        certifiedPersonalInfo: 'certifiedPersonalInfoId',
                        certifiedIndexationStartOfYear:
                            'certifiedIndexationStartOfYearId',
                        certifiedPensionCorrections:
                            'certifiedPensionCorrectionsId',
                        certifiedVoluntaryContributions:
                            'certifiedVoluntaryContributionsId',
                        certifiedPensionParameters:
                            'certifiedPensionParametersId',
                        certifiedAddress: 'certifiedAddressId',
                        certifiedChild: 'certifiedChildId',
                        certifiedPartnerInfo: 'certifiedPartnerInfoId',
                    }

                    const foreignKeyField = foreignKeyMap[entityType]
                    if (!foreignKeyField) return

                    if (Array.isArray(relatedEntity)) {
                        relatedEntity.forEach((entity) => {
                            deleteConditions.push({
                                [foreignKeyField]: entity.id,
                            })
                        })
                    } else {
                        deleteConditions.push({
                            [foreignKeyField]: relatedEntity.id,
                        })
                    }
                })

                // Add salary entries conditions
                if (
                    certifiedData.certifiedEmploymentInfo
                        ?.certifiedSalaryEntries
                ) {
                    certifiedData.certifiedEmploymentInfo.certifiedSalaryEntries.forEach(
                        (entry) => {
                            deleteConditions.push({
                                certifiedSalaryEntryId: entry.id,
                            })
                        }
                    )
                }

                // Delete all related CertificationRejectReason records
                const deletedReasons =
                    await prisma.certificationRejectReason.deleteMany({
                        where: {
                            OR: deleteConditions,
                        },
                    })

                totalRejectionReasonsDeleted += deletedReasons.count
            }
        })

        return {
            success: true,
            certificationYear,
            totalCertifiedDataRecords: certifiedDataList.length,
            totalEntitiesReverted,
            totalRejectionReasonsDeleted,
            message: `Successfully reverted all approved/rejected changes for certification year ${certificationYear}`,
        }
    }

    private async revertSingleEntity(
        prisma: any,
        modelName: string,
        entity: any,
        certifiedDataId: string,
        entityType: string,
        revertedEntities: Array<{
            certifiedDataId: string
            entityType: string
            entityId: string
            changes: {
                approvedChangesCleared: string[]
                requestedChangesCleared: string[]
            }
        }>
    ) {
        if (!entity) return

        const currentApprovedChanges = entity.approvedChanges || []
        const currentRequestedChanges = entity.requestedChanges || []

        // Only update if there are changes to revert
        if (
            currentApprovedChanges.length > 0 ||
            currentRequestedChanges.length > 0
        ) {
            await prisma[modelName].update({
                where: { id: entity.id },
                data: {
                    approvedChanges: [],
                    requestedChanges: [],
                },
            })

            // Track what was reverted
            revertedEntities.push({
                certifiedDataId,
                entityType,
                entityId: entity.id,
                changes: {
                    approvedChangesCleared: [...currentApprovedChanges],
                    requestedChangesCleared: [...currentRequestedChanges],
                },
            })
        }
    }

    async previewRevertChanges(certificationYear: number) {
        // Find all certified data for the given year
        const certifiedDataList = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: certificationYear,
            },
            include: {
                ...this.getCertifiedDataIncludes(),
                participant: {
                    select: {
                        id: true,
                        personalInfo: {
                            select: {
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
            },
        })

        if (certifiedDataList.length === 0) {
            throw new NotFoundException(
                `No certified data found for year ${certificationYear}`
            )
        }

        const certifiedEntityTypes = [
            'certifiedData',
            'certifiedPensionInfo',
            'certifiedEmploymentInfo',
            'certifiedPersonalInfo',
            'certifiedIndexationStartOfYear',
            'certifiedPensionCorrections',
            'certifiedVoluntaryContributions',
            'certifiedPensionParameters',
            'certifiedAddress',
            'certifiedChild',
            'certifiedPartnerInfo',
        ]

        const affectedEntities = []
        let totalEntitiesAffected = 0

        // Analyze each certified data record
        for (const certifiedData of certifiedDataList) {
            const participantName = certifiedData.participant?.personalInfo
                ? `${certifiedData.participant.personalInfo.firstName} ${certifiedData.participant.personalInfo.lastName}`
                : 'Unknown Participant'

            // Process each entity type
            for (const entityType of certifiedEntityTypes) {
                const relatedEntity = certifiedData[entityType]
                if (!relatedEntity) continue

                // Handle array relationships
                if (Array.isArray(relatedEntity)) {
                    for (const entity of relatedEntity) {
                        const analysis = this.analyzeEntityChanges(entity)
                        if (analysis.hasChanges) {
                            affectedEntities.push({
                                certifiedDataId: certifiedData.id,
                                participantName,
                                entityType,
                                entityId: entity.id,
                                approvedChangesCount:
                                    analysis.approvedChangesCount,
                                requestedChangesCount:
                                    analysis.requestedChangesCount,
                                approvedChanges: analysis.approvedChanges,
                                requestedChanges: analysis.requestedChanges,
                            })
                            totalEntitiesAffected++
                        }
                    }
                } else {
                    // Handle single relationships
                    const analysis = this.analyzeEntityChanges(relatedEntity)
                    if (analysis.hasChanges) {
                        affectedEntities.push({
                            certifiedDataId: certifiedData.id,
                            participantName,
                            entityType,
                            entityId: relatedEntity.id,
                            approvedChangesCount: analysis.approvedChangesCount,
                            requestedChangesCount:
                                analysis.requestedChangesCount,
                            approvedChanges: analysis.approvedChanges,
                            requestedChanges: analysis.requestedChanges,
                        })
                        totalEntitiesAffected++
                    }
                }
            }

            // Handle salary entries
            if (certifiedData.certifiedEmploymentInfo?.certifiedSalaryEntries) {
                for (const salaryEntry of certifiedData.certifiedEmploymentInfo
                    .certifiedSalaryEntries) {
                    const analysis = this.analyzeEntityChanges(salaryEntry)
                    if (analysis.hasChanges) {
                        affectedEntities.push({
                            certifiedDataId: certifiedData.id,
                            participantName,
                            entityType: 'certifiedSalaryEntry',
                            entityId: salaryEntry.id,
                            approvedChangesCount: analysis.approvedChangesCount,
                            requestedChangesCount:
                                analysis.requestedChangesCount,
                            approvedChanges: analysis.approvedChanges,
                            requestedChanges: analysis.requestedChanges,
                        })
                        totalEntitiesAffected++
                    }
                }
            }
        }

        const totalRejectionReasons =
            await this.countRejectionReasonsForYear(certificationYear)

        const estimatedImpact = this.generateImpactSummary(
            certifiedDataList.length,
            totalEntitiesAffected,
            totalRejectionReasons
        )

        return {
            certificationYear,
            totalCertifiedDataRecords: certifiedDataList.length,
            totalEntitiesAffected,
            totalRejectionReasons,
            affectedEntities: affectedEntities.slice(0, 50), // Limit to first 50 for performance
            estimatedImpact,
        }
    }

    private analyzeEntityChanges(entity: any) {
        const approvedChanges = entity?.approvedChanges || []
        const requestedChanges = entity?.requestedChanges || []

        return {
            hasChanges:
                approvedChanges.length > 0 || requestedChanges.length > 0,
            approvedChangesCount: approvedChanges.length,
            requestedChangesCount: requestedChanges.length,
            approvedChanges: [...approvedChanges],
            requestedChanges: [...requestedChanges],
        }
    }

    private async countRejectionReasonsForYear(
        certificationYear: number
    ): Promise<number> {
        // Get all certified data IDs for the year
        const certifiedDataIds = await this.prisma.certifiedData.findMany({
            where: { certificationYear },
            select: { id: true },
        })

        if (certifiedDataIds.length === 0) return 0

        // Count all rejection reasons related to this year's certified data
        const count = await this.prisma.certificationRejectReason.count({
            where: {
                OR: [
                    {
                        certifiedDataId: {
                            in: certifiedDataIds.map((cd) => cd.id),
                        },
                    },
                    {
                        certifiedPensionInfo: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedEmploymentInfo: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedPersonalInfo: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedIndexationStartOfYear: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedPensionCorrections: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedVoluntaryContributions: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedPensionParameters: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedSalaryEntry: {
                            certifiedEmploymentInfo: {
                                certifiedDataId: {
                                    in: certifiedDataIds.map((cd) => cd.id),
                                },
                            },
                        },
                    },
                    {
                        certifiedPartnerInfo: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedChild: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                    {
                        certifiedAddress: {
                            certifiedDataId: {
                                in: certifiedDataIds.map((cd) => cd.id),
                            },
                        },
                    },
                ],
            },
        })

        return count
    }

    private generateImpactSummary(
        totalRecords: number,
        entitiesAffected: number,
        rejectionReasons: number
    ): string {
        if (entitiesAffected === 0 && rejectionReasons === 0) {
            return `No changes to revert for this certification year.`
        }

        let impact = `This operation will affect ${totalRecords} certification records. `

        if (entitiesAffected > 0) {
            impact += `${entitiesAffected} entities will have their approved/requested changes cleared. `
        }

        if (rejectionReasons > 0) {
            impact += `${rejectionReasons} rejection reasons will be permanently deleted. `
        }

        impact += `This action cannot be undone.`

        return impact
    }

    async revertSingleField(
        input: RevertSingleFieldInput,
        userId: string
    ): Promise<RevertSingleFieldResponse> {
        const { entityId, entityType, path } = input

        // Map of entity types to their Prisma model names
        const entityModelMap: Record<string, string> = {
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedAddress: 'certifiedAddress',
            certifiedChild: 'certifiedChild',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
        }

        const modelName = entityModelMap[entityType]
        if (!modelName) {
            throw new BadRequestException(
                `Unsupported entity type: ${entityType}`
            )
        }

        // Find the entity
        const entity = await this.prisma[modelName].findUnique({
            where: { id: entityId },
        })

        if (!entity) {
            throw new NotFoundException(`Entity with ID ${entityId} not found`)
        }

        const currentApprovedChanges = entity.approvedChanges || []
        const currentRequestedChanges = entity.requestedChanges || []

        // Remove the specific path from both arrays
        const updatedApprovedChanges = currentApprovedChanges.filter(
            (field: string) => field !== path
        )
        const updatedRequestedChanges = currentRequestedChanges.filter(
            (field: string) => field !== path
        )

        // Only update if there are changes to revert
        if (
            currentApprovedChanges.length !== updatedApprovedChanges.length ||
            currentRequestedChanges.length !== updatedRequestedChanges.length
        ) {
            await this.prisma[modelName].update({
                where: { id: entityId },
                data: {
                    approvedChanges: updatedApprovedChanges,
                    requestedChanges: updatedRequestedChanges,
                },
            })

            // Also invalidate any reject reasons for this field
            if (entityType.startsWith('Certified')) {
                const entityTypeForRejectReason =
                    entityType.charAt(0).toLowerCase() + entityType.slice(1)
                await this.certificationRejectReasonService.invalidateRejectReasonsForField(
                    entityId,
                    path,
                    entityTypeForRejectReason
                )
            }

            return {
                success: true,
                entityId,
                entityType,
                path,
                message: `Successfully reverted changes for field ${path}`,
            }
        }

        return {
            success: false,
            entityId,
            entityType,
            path,
            message: `No changes found for field ${path}`,
        }
    }

    // New bulk certification methods
    async bulkStartCertification(
        participantIds: string[],
        year: number,
        userId: string
    ) {
        const successful: string[] = []
        const failed: { participantId: string; reason: string }[] = []

        for (const participantId of participantIds) {
            try {
                // Check if certification already exists
                const existingCertification =
                    await this.prisma.certifiedData.findFirst({
                        where: {
                            participantId,
                            certificationYear: year,
                        },
                    })

                if (existingCertification) {
                    if (
                        existingCertification.certificationStatus === 'pending'
                    ) {
                        // Update status to started
                        await this.prisma.certifiedData.update({
                            where: { id: existingCertification.id },
                            data: {
                                certificationStatus: 'started',
                                certifiedById: userId,
                            },
                        })
                        successful.push(participantId)
                    } else {
                        failed.push({
                            participantId,
                            reason: `Certification already ${existingCertification.certificationStatus}`,
                        })
                    }
                } else {
                    failed.push({
                        participantId,
                        reason: 'No certification data found for this year',
                    })
                }
            } catch (error) {
                failed.push({
                    participantId,
                    reason: `Error: ${error.message}`,
                })
            }
        }

        return { successful, failed }
    }

    async bulkApproveCertification(certificationIds: string[], userId: string) {
        const successful: string[] = []
        const failed: { certificationId: string; reason: string }[] = []

        for (const certificationId of certificationIds) {
            try {
                const certification =
                    await this.prisma.certifiedData.findUnique({
                        where: { id: certificationId },
                    })

                if (!certification) {
                    failed.push({
                        certificationId,
                        reason: 'Certification not found',
                    })
                    continue
                }

                if (certification.certificationStatus !== 'started') {
                    failed.push({
                        certificationId,
                        reason: `Cannot approve certification with status: ${certification.certificationStatus}`,
                    })
                    continue
                }

                // Update certification status to completed
                await this.prisma.certifiedData.update({
                    where: { id: certificationId },
                    data: {
                        certificationStatus: 'completed',
                        certifiedById: userId,
                    },
                })

                successful.push(certificationId)
            } catch (error) {
                failed.push({
                    certificationId,
                    reason: `Error: ${error.message}`,
                })
            }
        }

        return { successful, failed }
    }

    async bulkRejectCertification(
        certificationIds: string[],
        reason: string,
        userId: string
    ) {
        const successful: string[] = []
        const failed: { certificationId: string; reason: string }[] = []

        for (const certificationId of certificationIds) {
            try {
                const certification =
                    await this.prisma.certifiedData.findUnique({
                        where: { id: certificationId },
                    })

                if (!certification) {
                    failed.push({
                        certificationId,
                        reason: 'Certification not found',
                    })
                    continue
                }

                if (certification.certificationStatus !== 'started') {
                    failed.push({
                        certificationId,
                        reason: `Cannot reject certification with status: ${certification.certificationStatus}`,
                    })
                    continue
                }

                // Update certification status back to pending with reject reason
                await this.prisma.certifiedData.update({
                    where: { id: certificationId },
                    data: {
                        certificationStatus: 'pending',
                        certifiedById: userId,
                    },
                })

                // Create reject reason record
                await this.certificationRejectReasonService.create({
                    certifiedDataId: certificationId,
                    field: 'certification',
                    reason,
                })

                successful.push(certificationId)
            } catch (error) {
                failed.push({
                    certificationId,
                    reason: `Error: ${error.message}`,
                })
            }
        }

        return { successful, failed }
    }

    async bulkApproveCertifiedFields(input: {
        operations: Array<{
            certifiedDataId: string
            entityType: string
            fields: string[]
            entityId?: string
        }>
    }) {
        const failed: {
            certifiedDataId: string
            entityType: string
            message: string
        }[] = []
        let successCount = 0
        for (const op of input.operations || []) {
            try {
                await this.updateApprovedChanges(
                    op.certifiedDataId,
                    op.fields,
                    op.entityType,
                    op.entityId
                )
                successCount++
            } catch (e: any) {
                failed.push({
                    certifiedDataId: op.certifiedDataId,
                    entityType: op.entityType,
                    message: e?.message || 'Unknown error',
                })
            }
        }
        return { successCount, failureCount: failed.length, failed }
    }

    async bulkRejectCertifiedFields(input: {
        operations: Array<{
            certifiedDataId: string
            entityType: string
            fields: string[]
            entityId?: string
        }>
        rejectReason: string
    }) {
        const failed: {
            certifiedDataId: string
            entityType: string
            message: string
        }[] = []
        let successCount = 0
        const dummyUserId = 'be196c0f-ee7b-42a3-9163-885f649e65ef'
        for (const op of input.operations || []) {
            try {
                await this.updateRejectedChanges(
                    op.certifiedDataId,
                    op.fields,
                    op.entityType,
                    input.rejectReason,
                    dummyUserId,
                    op.entityId
                )
                successCount++
            } catch (e: any) {
                failed.push({
                    certifiedDataId: op.certifiedDataId,
                    entityType: op.entityType,
                    message: e?.message || 'Unknown error',
                })
            }
        }
        return { successCount, failureCount: failed.length, failed }
    }

    async getCertificationStats(year: number) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: year,
            },
            select: {
                id: true,
                certificationStatus: true,
                participantId: true,
                certifiedPensionInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedEmploymentInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedPersonalInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
            },
        })

        const stats = {
            totalParticipants: certifications.length,
            pendingCertifications: certifications.filter(
                (c) => c.certificationStatus === 'pending'
            ).length,
            inProgress: certifications.filter(
                (c) => c.certificationStatus === 'started'
            ).length,
            completed: certifications.filter(
                (c) => c.certificationStatus === 'completed'
            ).length,
            requiresAttention: certifications.filter((c) => {
                const pensionChanges =
                    (c.certifiedPensionInfo?.approvedChanges?.length || 0) +
                    (c.certifiedPensionInfo?.pendingChanges?.length || 0) +
                    (c.certifiedPensionInfo?.requestedChanges?.length || 0)
                const employmentChanges =
                    (c.certifiedEmploymentInfo?.approvedChanges?.length || 0) +
                    (c.certifiedEmploymentInfo?.pendingChanges?.length || 0) +
                    (c.certifiedEmploymentInfo?.requestedChanges?.length || 0)
                const personalChanges =
                    (c.certifiedPersonalInfo?.approvedChanges?.length || 0) +
                    (c.certifiedPersonalInfo?.pendingChanges?.length || 0) +
                    (c.certifiedPersonalInfo?.requestedChanges?.length || 0)
                return pensionChanges + employmentChanges + personalChanges > 5
            }).length,
        }

        return stats
    }

    async getAutoApproveEligible(year: number) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: year,
                certificationStatus: 'started',
            },
            include: {
                participant: {
                    select: {
                        id: true,
                        personalInfo: {
                            select: {
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
                certifiedPensionInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedEmploymentInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedPersonalInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
            },
        })

        // Filter for auto-approve eligible (no changes or minimal changes)
        const eligible = certifications.filter((certification) => {
            const pensionChanges =
                (certification.certifiedPensionInfo?.approvedChanges?.length ||
                    0) +
                (certification.certifiedPensionInfo?.pendingChanges?.length ||
                    0) +
                (certification.certifiedPensionInfo?.requestedChanges?.length ||
                    0)
            const employmentChanges =
                (certification.certifiedEmploymentInfo?.approvedChanges
                    ?.length || 0) +
                (certification.certifiedEmploymentInfo?.pendingChanges
                    ?.length || 0) +
                (certification.certifiedEmploymentInfo?.requestedChanges
                    ?.length || 0)
            const personalChanges =
                (certification.certifiedPersonalInfo?.approvedChanges?.length ||
                    0) +
                (certification.certifiedPersonalInfo?.pendingChanges?.length ||
                    0) +
                (certification.certifiedPersonalInfo?.requestedChanges
                    ?.length || 0)
            const totalChanges =
                pensionChanges + employmentChanges + personalChanges
            return totalChanges === 0 || totalChanges < 3
        })

        return eligible.map((cert) => {
            const pensionChanges =
                (cert.certifiedPensionInfo?.approvedChanges?.length || 0) +
                (cert.certifiedPensionInfo?.pendingChanges?.length || 0) +
                (cert.certifiedPensionInfo?.requestedChanges?.length || 0)
            const employmentChanges =
                (cert.certifiedEmploymentInfo?.approvedChanges?.length || 0) +
                (cert.certifiedEmploymentInfo?.pendingChanges?.length || 0) +
                (cert.certifiedEmploymentInfo?.requestedChanges?.length || 0)
            const personalChanges =
                (cert.certifiedPersonalInfo?.approvedChanges?.length || 0) +
                (cert.certifiedPersonalInfo?.pendingChanges?.length || 0) +
                (cert.certifiedPersonalInfo?.requestedChanges?.length || 0)

            return {
                certificationId: cert.id,
                participantId: cert.participantId,
                participantName: `${cert.participant.personalInfo.firstName} ${cert.participant.personalInfo.lastName}`,
                changes: pensionChanges + employmentChanges + personalChanges,
            }
        })
    }

    async getCommonChangePatterns(year: number) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                certificationYear: year,
                certificationStatus: 'started',
            },
            select: {
                id: true,
                participantId: true,
                certifiedPensionInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedPersonalInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
                certifiedEmploymentInfo: {
                    select: {
                        approvedChanges: true,
                        pendingChanges: true,
                        requestedChanges: true,
                    },
                },
            },
        })

        // Analyze common change patterns
        const changePatterns = new Map<
            string,
            {
                field: string
                count: number
                certificationIds: string[]
            }
        >()

        certifications.forEach((cert) => {
            const allChanges = [
                ...(cert.certifiedPensionInfo?.approvedChanges || []),
                ...(cert.certifiedPensionInfo?.pendingChanges || []),
                ...(cert.certifiedPensionInfo?.requestedChanges || []),
                ...(cert.certifiedPersonalInfo?.approvedChanges || []),
                ...(cert.certifiedPersonalInfo?.pendingChanges || []),
                ...(cert.certifiedPersonalInfo?.requestedChanges || []),
                ...(cert.certifiedEmploymentInfo?.approvedChanges || []),
                ...(cert.certifiedEmploymentInfo?.pendingChanges || []),
                ...(cert.certifiedEmploymentInfo?.requestedChanges || []),
            ]

            allChanges.forEach((change) => {
                if (!changePatterns.has(change)) {
                    changePatterns.set(change, {
                        field: change,
                        count: 0,
                        certificationIds: [],
                    })
                }
                const pattern = changePatterns.get(change)!
                pattern.count++
                pattern.certificationIds.push(cert.id)
            })
        })

        // Convert to array and sort by count
        return Array.from(changePatterns.values())
            .filter((pattern) => pattern.count > 1) // Only show patterns with multiple occurrences
            .sort((a, b) => b.count - a.count)
    }

    async getBulkCertificationData(participantIds: string[], year: number) {
        const certifications = await this.prisma.certifiedData.findMany({
            where: {
                participantId: { in: participantIds },
                certificationYear: { in: [year, year - 1] },
            },
            orderBy: [{ participantId: 'asc' }, { certificationYear: 'desc' }],
            include: this.getCertifiedDataIncludes(),
        })

        // Group by participant ID
        const participantDataMap = new Map<string, any[]>()
        certifications.forEach((cert) => {
            console.log({ partMap: participantDataMap })
            if (!participantDataMap.has(cert.participantId)) {
                participantDataMap.set(cert.participantId, [])
            }
            participantDataMap.get(cert.participantId)?.push(cert)
        })

        // Process each participant's data
        const result = []
        let totalChanges = 0
        let noChangesCount = 0
        let highRiskCount = 0
        const allChangePatterns = new Map<
            string,
            {
                field: string
                count: number
                participants: any[]
            }
        >()

        for (const [participantId, certs] of participantDataMap) {
            const currentYearData = certs.find(
                (c) => c.certificationYear === year
            )
            const previousYearData = certs.find(
                (c) => c.certificationYear === year - 1
            )

            if (currentYearData) {
                if (previousYearData) {
                    this.markDifferences(currentYearData, previousYearData)
                }

                const participantChanges = this.countAllChanges(currentYearData)
                totalChanges += participantChanges

                if (participantChanges === 0) {
                    noChangesCount++
                }

                // Determine risk level based on changes count
                let risk = 'low'
                if (participantChanges > 10) {
                    risk = 'high'
                    highRiskCount++
                } else if (participantChanges > 5) {
                    risk = 'medium'
                }

                this.collectChangePatterns(
                    currentYearData,
                    previousYearData,
                    allChangePatterns,
                    year
                )

                result.push({
                    ...currentYearData,
                    totalChanges: participantChanges,
                    risk,
                })
            }
        }

        // Convert change patterns to array and sort by count
        const changePatterns = Array.from(allChangePatterns.values()).sort(
            (a, b) => b.count - a.count
        )

        return {
            certifications: result,
            stats: {
                totalParticipants: result.length,
                totalChanges,
                noChanges: noChangesCount,
                highRisk: highRiskCount,
                year,
            },
            changePatterns,
        }
    }

    private countAllChanges(certifiedData: any): number {
        let count = 0

        // Count differences in nested objects
        const nestedObjects = [
            'certifiedPensionInfo',
            'certifiedEmploymentInfo',
            'certifiedPersonalInfo',
            'certifiedAddress',
            'certifiedIndexationStartOfYear',
            'certifiedPensionCorrections',
            'certifiedVoluntaryContributions',
            'certifiedPensionParameters',
        ]

        nestedObjects.forEach((field) => {
            if (certifiedData[field]?.differences) {
                count += certifiedData[field].differences.length
            }
        })

        // Count array field differences
        if (certifiedData.certifiedChildDifferences) {
            count += certifiedData.certifiedChildDifferences.length
        }
        if (certifiedData.certifiedPartnerInfoDifferences) {
            count += certifiedData.certifiedPartnerInfoDifferences.length
        }

        return count
    }

    private collectChangePatterns(
        currentYearData: any,
        previousYearData: any | null,
        changePatterns: Map<string, any>,
        year: number
    ) {
        const participantInfo = {
            id: currentYearData.participantId,
            name: `${currentYearData.certifiedPersonalInfo?.firstName || ''} ${
                currentYearData.certifiedPersonalInfo?.lastName || ''
            }`.trim(),
        }

        // Fields to exclude from comparison
        const excludedFields = [
            'pendingChanges',
            'requestedChanges',
            'approvedChanges',
            'certificationRejectReason',
        ]

        // Collect changes from nested objects
        const nestedObjects = [
            'certifiedPensionInfo',
            'certifiedEmploymentInfo',
            'certifiedPersonalInfo',
            'certifiedAddress',
            'certifiedIndexationStartOfYear',
            'certifiedPensionCorrections',
            'certifiedVoluntaryContributions',
            'certifiedPensionParameters',
        ]

        const prevYear = year - 1

        nestedObjects.forEach((objectName) => {
            const obj = currentYearData?.[objectName]
            if (obj?.differences) {
                obj.differences.forEach((field: string) => {
                    // Skip excluded fields and '_exists'
                    if (field === '_exists' || excludedFields.includes(field)) {
                        return
                    }

                    // Skip certifiedSalaryEntries as it will be handled separately
                    if (field === 'certifiedSalaryEntries') {
                        return
                    }

                    const key = `${objectName}.${field}`
                    if (!changePatterns.has(key)) {
                        changePatterns.set(key, {
                            field: key,
                            count: 0,
                            participants: [],
                        })
                    }
                    const pattern = changePatterns.get(key)!
                    pattern.count++

                    const currentValue = this.getFieldSnapshotValue(
                        currentYearData,
                        objectName,
                        field,
                        year
                    )
                    const previousValue = previousYearData
                        ? this.getFieldSnapshotValue(
                              previousYearData,
                              objectName,
                              field,
                              prevYear
                          )
                        : null

                    // Check if field is in requestedChanges or approvedChanges arrays
                    const requestedChanges = obj.requestedChanges || []
                    const approvedChanges = obj.approvedChanges || []

                    const isRequested = requestedChanges.includes(field)
                    const isApproved = approvedChanges.includes(field)

                    pattern.participants.push({
                        ...participantInfo,
                        previousValue,
                        currentValue,
                        requestedChanges: isRequested,
                        approvedChanges: isApproved,
                    })
                })
            }
        })

        // Handle certifiedSalaryEntries separately
        this.collectSalaryEntriesChangePatterns(
            currentYearData,
            previousYearData,
            changePatterns,
            year,
            participantInfo,
            excludedFields
        )
    }

    private collectSalaryEntriesChangePatterns(
        currentYearData: any,
        previousYearData: any | null,
        changePatterns: Map<string, any>,
        year: number,
        participantInfo: any,
        excludedFields: string[]
    ) {
        const currentEmploymentInfo = currentYearData?.certifiedEmploymentInfo
        const previousEmploymentInfo = previousYearData?.certifiedEmploymentInfo

        if (!currentEmploymentInfo?.certifiedSalaryEntries) {
            return
        }

        // Process each salary entry that has differences
        currentEmploymentInfo.certifiedSalaryEntries.forEach((salaryEntry: any) => {
            if (!salaryEntry.differences || salaryEntry.differences.length === 0) {
                return
            }

            // Find corresponding previous salary entry by year
            let previousSalaryEntry = null
            if (previousEmploymentInfo?.certifiedSalaryEntries) {
                previousSalaryEntry = previousEmploymentInfo.certifiedSalaryEntries.find(
                    (entry: any) => entry.year === salaryEntry.year
                )
            }

            salaryEntry.differences.forEach((field: string) => {
                // Skip excluded fields and '_exists'
                if (field === '_exists' || excludedFields.includes(field)) {
                    return
                }

                const key = `certifiedSalaryEntries[${salaryEntry.year}].${field}`
                if (!changePatterns.has(key)) {
                    changePatterns.set(key, {
                        field: key,
                        count: 0,
                        participants: [],
                    })
                }
                const pattern = changePatterns.get(key)!
                pattern.count++

                const currentValue = salaryEntry[field]
                const previousValue = previousSalaryEntry ? previousSalaryEntry[field] : null

                // Check if field is in requestedChanges or approvedChanges arrays
                const requestedChanges = salaryEntry.requestedChanges || []
                const approvedChanges = salaryEntry.approvedChanges || []

                const isRequested = requestedChanges.includes(field)
                const isApproved = approvedChanges.includes(field)

                pattern.participants.push({
                    ...participantInfo,
                    year: salaryEntry.year,
                    previousValue,
                    currentValue,
                    requestedChanges: isRequested,
                    approvedChanges: isApproved,
                })
            })
        })
    }

    private getFieldSnapshotValue(
        container: any,
        objectName: string,
        field: string,
        snapshotYear?: number
    ): any {
        const obj = container?.[objectName]
        if (!obj) return null
        const val = obj[field]
        if (Array.isArray(val)) {
            if (
                val.length > 0 &&
                typeof val[0] === 'object' &&
                'year' in val[0] &&
                typeof snapshotYear === 'number'
            ) {
                return val.filter((item: any) => item?.year === snapshotYear)
            }
            return val
        }
        return val ?? null
    }

    async getCertificationParticipants(input: any) {
        const {
            year,
            skip = 0,
            take = 100,
            status,
            department,
            searchQuery,
            sortBy = 'lastName',
            sortOrder = 'asc',
            excludeCompleted = false,
            forStartCertification = false,
        } = input
        const currentYear = new Date().getFullYear()
        const yearNum = typeof year === 'string' ? parseInt(year, 10) : year

        console.log('getCertificationParticipants called with:', {
            year: yearNum,
            currentYear,
            isCurrentYear: yearNum === currentYear,
            excludeCompleted,
            forStartCertification,
        })

        // For Start Certification, don't show current year participants
        if (forStartCertification && yearNum === currentYear) {
            return []
        }

        // If year is current year, fetch from Participant table
        if (yearNum === currentYear) {
            const whereClause: any = {}

            // Add department filter if provided
            if (department) {
                whereClause.employmentInfo = {
                    department: {
                        contains: department,
                        mode: 'insensitive',
                    },
                }
            }

            // Add search query if provided
            if (searchQuery) {
                whereClause.OR = [
                    {
                        personalInfo: {
                            firstName: {
                                contains: searchQuery,
                                mode: 'insensitive',
                            },
                        },
                    },
                    {
                        personalInfo: {
                            lastName: {
                                contains: searchQuery,
                                mode: 'insensitive',
                            },
                        },
                    },
                    {
                        personalInfo: {
                            email: {
                                contains: searchQuery,
                                mode: 'insensitive',
                            },
                        },
                    },
                ]
            }

            const participants = await this.prisma.participant.findMany({
                skip,
                take,
                where: whereClause,
                include: {
                    personalInfo: true,
                    employmentInfo: true,
                },
                orderBy:
                    sortBy === 'lastName'
                        ? { personalInfo: { lastName: sortOrder } }
                        : sortBy === 'firstName'
                          ? { personalInfo: { firstName: sortOrder } }
                          : { createdAt: sortOrder },
            })

            // Map participants to CertificationParticipant format
            return participants.map((participant) => ({
                id: participant.id,
                certificationId: null,
                certificationStatus: 'pending',
                risk: 'low',
                lastActivity: null,
                department: participant.employmentInfo?.department || null,
                email: participant.personalInfo?.email || null,
                firstName: participant.personalInfo?.firstName || null,
                lastName: participant.personalInfo?.lastName || null,
            }))
        } else {
            const certifiedData = await this.prisma.certifiedData.findMany({
                skip,
                take,
                where: {
                    certificationYear: year,
                    ...(status && { certificationStatus: status }),
                    // Exclude completed certifications if requested (for Start Certification)
                    ...(excludeCompleted && {
                        certificationStatus: { not: 'completed' },
                    }),
                    ...(department && {
                        participant: {
                            employmentInfo: {
                                department: {
                                    contains: department,
                                    mode: 'insensitive' as any,
                                },
                            },
                        },
                    }),
                    ...(searchQuery && {
                        participant: {
                            personalInfo: {
                                OR: [
                                    {
                                        firstName: {
                                            contains: searchQuery,
                                            mode: 'insensitive' as any,
                                        },
                                    },
                                    {
                                        lastName: {
                                            contains: searchQuery,
                                            mode: 'insensitive' as any,
                                        },
                                    },
                                    {
                                        email: {
                                            contains: searchQuery,
                                            mode: 'insensitive' as any,
                                        },
                                    },
                                ],
                            },
                        },
                    }),
                },
                include: {
                    participant: {
                        include: {
                            personalInfo: true,
                            employmentInfo: true,
                        },
                    },
                },
                orderBy:
                    sortBy === 'lastName'
                        ? {
                              participant: {
                                  personalInfo: { lastName: sortOrder },
                              },
                          }
                        : sortBy === 'firstName'
                          ? {
                                participant: {
                                    personalInfo: { firstName: sortOrder },
                                },
                            }
                          : sortBy === 'certificationStatus'
                            ? { certificationStatus: sortOrder }
                            : { certifiedAt: sortOrder },
            })

            // Map certified data to CertificationParticipant format
            return certifiedData.map((cd) => {
                // Determine risk based on certification status and notes
                let risk = 'low'
                if (cd.certificationStatus === 'rejected') {
                    risk = 'high'
                } else if (cd.certificationStatus === 'started') {
                    risk = 'medium'
                }

                return {
                    id: cd.participantId,
                    certificationId: cd.id,
                    certificationStatus: cd.certificationStatus || 'pending',
                    risk,
                    lastActivity: cd.certifiedAt.toISOString(),
                    department:
                        cd.participant.employmentInfo?.department || null,
                    email: cd.participant.personalInfo?.email || null,
                    firstName: cd.participant.personalInfo?.firstName || null,
                    lastName: cd.participant.personalInfo?.lastName || null,
                }
            })
        }
    }
}

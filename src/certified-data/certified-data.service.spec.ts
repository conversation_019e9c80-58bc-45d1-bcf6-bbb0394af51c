import { Test, TestingModule } from '@nestjs/testing'
import { CertifiedDataService } from './certified-data.service'

describe('CertifiedDataService', () => {
    let service: CertifiedDataService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [CertifiedDataService],
        }).compile()

        service = module.get<CertifiedDataService>(CertifiedDataService)
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})

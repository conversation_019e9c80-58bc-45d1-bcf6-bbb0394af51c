import { ObjectType, Field, ID } from '@nestjs/graphql'

@ObjectType()
export class CertificationParticipant {
  @Field(() => ID)
  id: string

  @Field({ nullable: true })
  certificationId?: string | null

  @Field(() => String, { nullable: true })
  certificationStatus?: string

  @Field(() => String, { nullable: true })
  risk?: string

  @Field(() => String, { nullable: true })
  lastActivity?: string

  @Field(() => String, { nullable: true })
  department?: string

  @Field(() => String, { nullable: true })
  email?: string

  @Field(() => String, { nullable: true })
  firstName?: string

  @Field(() => String, { nullable: true })
  lastName?: string
}


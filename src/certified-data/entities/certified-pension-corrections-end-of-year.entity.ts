import { ObjectType, Field, ID, Float } from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedPensionCorrectionsEndOfYear {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    attainableGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualPartnersPension?: number

    @Field(() => Float, { nullable: true })
    accruedGrossAnnualSinglesPension?: number

    @Field(() => Float, { nullable: true })
    grossAnnualDisabilityPension?: number

    @Field(() => Float, { nullable: true })
    extraAccruedGrossAnnualOldAgePension?: number

    @Field(() => Float, { nullable: true })
    extraAccruedGrossAnnualPartnersPension?: number

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}

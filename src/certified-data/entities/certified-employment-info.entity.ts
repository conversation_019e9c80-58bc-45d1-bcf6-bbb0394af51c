import { ObjectType, Field, ID, Int, GraphQLISODateTime } from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'
import { GraphQLJSON } from 'graphql-type-json'
import { CertifiedSalaryEntry } from './certified-salary-entry.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedEmploymentInfo {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field({ nullable: true })
    employeeId?: string

    @Field({ nullable: true })
    department?: string

    @Field({ nullable: true })
    position?: string

    @Field(() => Int, { nullable: true })
    regNum?: number

    @Field(() => Int, { nullable: true })
    havNum?: number

    @Field(() => GraphQLISODateTime, { nullable: true })
    startDate?: Date

    @Field(() => GraphQLISODateTime, { nullable: true })
    endDate?: Date

    @Field({ nullable: true })
    status?: string

    @Field(() => [CertifiedSalaryEntry], { nullable: true })
    certifiedSalaryEntries?: CertifiedSalaryEntry[]

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}

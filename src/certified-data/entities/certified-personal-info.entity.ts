import { ObjectType, Field, ID, Int } from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'
import { CertifiedChild } from './certified-child.entity'
import { CertifiedPartnerInfo } from './certified-partner-info.entity'
import { CertifiedAddress } from './certified-address.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedPersonalInfo {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field({ nullable: true })
    sex?: string

    @Field({ nullable: true })
    email?: string

    @Field({ nullable: true })
    phone?: string

    @Field({ nullable: true })
    maritalStatus?: string

    @Field(() => Int, { nullable: true })
    birthDay?: number

    @Field(() => Int, { nullable: true })
    birthMonth?: number

    @Field(() => Int, { nullable: true })
    birthYear?: number

    @Field(() => [CertifiedAddress], { nullable: true })
    address?: any

    @Field(() => [CertifiedPartnerInfo], { nullable: true })
    partnerInfo?: CertifiedPartnerInfo[]

    @Field(() => [CertifiedChild], { nullable: true })
    children?: any

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}

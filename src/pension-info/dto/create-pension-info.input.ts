import { InputType, Field, ID, Int } from '@nestjs/graphql'
import {
    IsNotEmpty,
    IsString,
    IsOptional,
    IsInt,
    IsDate,
} from 'class-validator'

@InputType()
export class CreatePensionInfoInput {
    @Field(() => ID)
    @IsString()
    @IsNotEmpty()
    participantId: string

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    code?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    previousCode?: number

    @Field({ nullable: true })
    @IsDate()
    @IsOptional()
    codeEffectiveDate?: Date

    @Field({ nullable: true })
    @IsDate()
    @IsOptional()
    previousCodeEffectiveDate?: Date

    @Field({ nullable: true })
    @IsDate()
    @IsOptional()
    accrualStartDate?: Date

    @Field({ nullable: true })
    @IsDate()
    @IsOptional()
    accrualEndDate?: Date

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    codeDescription?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    codeImpact?: string
}

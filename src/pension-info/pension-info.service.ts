import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionInfoInput } from './dto/create-pension-info.input'
import { UpdatePensionInfoInput } from './dto/update-pension-info.input'
import * as R from 'ramda'

@Injectable()
export class PensionInfoService {
    constructor(private readonly prisma: PrismaService) {}

    create(createPensionInfoInput: CreatePensionInfoInput) {
        return this.prisma.pensionInfo.create({
            data: createPensionInfoInput,
            include: {
                participant: true,
                certificationRejectReason: true,
            },
        })
    }

    findAll() {
        return this.prisma.pensionInfo.findMany({
            include: {
                participant: true,
                certificationRejectReason: true,
            },
        })
    }

    findOne(id: string) {
        const pensionInfo = this.prisma.pensionInfo.findUnique({
            where: { id },
            include: {
                participant: true,
                certificationRejectReason: true,
            },
        })
        if (!pensionInfo) {
            throw new NotFoundException(`PensionInfo with ID ${id} not found`)
        }
        return pensionInfo
    }

    async update(updatePensionInfoInput: UpdatePensionInfoInput) {
        const existingPensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id: updatePensionInfoInput.id },
        })
        if (!existingPensionInfo) {
            throw new NotFoundException(
                `PensionInfo with ID ${updatePensionInfoInput.id} not found`
            )
        }

        // Handle code mutations if code field is being updated
        let dataToUpdate: any = { ...updatePensionInfoInput }
        if (
            updatePensionInfoInput.code !== undefined &&
            updatePensionInfoInput.code !== existingPensionInfo.code
        ) {
            const codeMutations = this.handleCodeMutation(
                existingPensionInfo.code,
                updatePensionInfoInput.code
            )
            dataToUpdate = { ...dataToUpdate, ...codeMutations }
        }

        return this.prisma.pensionInfo.update({
            where: { id: updatePensionInfoInput.id },
            data: dataToUpdate,
            include: {
                participant: true,
                certificationRejectReason: true,
            },
        })
    }

    remove(id: string) {
        const pensionInfo = this.prisma.pensionInfo.findUnique({
            where: { id },
        })
        if (!pensionInfo) {
            throw new NotFoundException(`PensionInfo with ID ${id} not found`)
        }
        return this.prisma.pensionInfo.delete({
            where: { id },
            include: {
                participant: true,
                certificationRejectReason: true,
            },
        })
    }
    async updatePendingChanges(id: string, paths: string[]) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id },
        })

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        const currentPendingChanges = pensionInfo.pendingChanges || []
        const updatedPendingChanges = R.uniq([
            ...currentPendingChanges,
            ...paths,
        ])

        await this.prisma.pensionInfo.update({
            where: { id },
            data: {
                pendingChanges: updatedPendingChanges,
            },
        })
    }

    async changeUpdate({
        entityId,
        path,
        newValue,
    }: {
        entityId: string
        path: string
        newValue: any
    }) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id: entityId },
        })
        const floatNewVal = parseFloat(newValue)

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        let updateData = path
            .split('.')
            .reduceRight<any>((acc, key) => ({ [key]: acc }), floatNewVal)

        // If updating the code field, handle the code mutations
        if (path === 'code') {
            const codeMutations = this.handleCodeMutation(
                pensionInfo.code,
                floatNewVal
            )
            updateData = { ...updateData, ...codeMutations }
        }

        await this.prisma.pensionInfo.update({
            where: { id: entityId },
            data: updateData,
        })
    }

    async clearPendingChanges(id: string, paths: string[]) {
        const pensionInfo = await this.prisma.pensionInfo.findUnique({
            where: { id },
        })

        if (!pensionInfo) {
            throw new Error('PensionInfo not found')
        }

        const currentPendingChanges = pensionInfo.pendingChanges || []
        const updatedPendingChanges = currentPendingChanges.filter(
            (path) => !paths.includes(path)
        )

        await this.prisma.pensionInfo.update({
            where: { id },
            data: {
                pendingChanges: updatedPendingChanges,
            },
        })
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // Find the pension info record by participant ID
        const pensionInfo = await this.prisma.pensionInfo.findFirst({
            where: { participantId },
        })

        if (!pensionInfo) {
            throw new NotFoundException(
                `PensionInfo with participant ID ${participantId} not found`
            )
        }

        // Update the specific field
        let updateData: any = {
            [path]: newValue,
        }

        // If updating the code field, handle the code mutations
        if (path === 'code' && pensionInfo.code !== newValue) {
            const codeMutations = this.handleCodeMutation(
                pensionInfo.code,
                newValue
            )
            updateData = { ...updateData, ...codeMutations }
        }

        return this.prisma.pensionInfo.update({
            where: { id: pensionInfo.id },
            data: updateData,
        })
    }

    /**
     * Handles code mutations and returns the appropriate date updates
     * Rules:
     * 1. If updating from code 10 to 30: set accrualEndDate to current date
     * 2. If updating from code 30 to 11: set accrualStartDate to current date and clear accrualEndDate
     * 3. If updating from any code to 70: set accrualEndDate to current date
     */
    private handleCodeMutation(oldCode: number | null, newCode: number): any {
        const currentDate = new Date()
        const mutations: any = {}

        // Rule 1: From code 10 to 30 - set accrualEndDate
        if (oldCode === 10 && newCode === 30) {
            mutations.accrualEndDate = currentDate
        }
        // Rule 2: From code 30 to 11 - set accrualStartDate and clear accrualEndDate
        else if (oldCode === 30 && newCode === 11) {
            mutations.accrualStartDate = currentDate
            mutations.accrualEndDate = null
        }
        // Rule 3: From any code to 70 - set accrualEndDate
        else if (newCode === 70 || newCode === 40) {
            mutations.accrualEndDate = currentDate
        }

        return mutations
    }
}

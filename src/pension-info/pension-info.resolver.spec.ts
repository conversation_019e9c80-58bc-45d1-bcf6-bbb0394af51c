import { Test, TestingModule } from '@nestjs/testing'
import { PensionInfoResolver } from './pension-info.resolver'
import { PensionInfoService } from './pension-info.service'

describe('PensionInfoResolver', () => {
    let resolver: PensionInfoResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PensionInfoResolver, PensionInfoService],
        }).compile()

        resolver = module.get<PensionInfoResolver>(PensionInfoResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

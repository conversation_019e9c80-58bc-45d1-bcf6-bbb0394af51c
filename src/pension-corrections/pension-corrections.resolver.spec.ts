import { Test, TestingModule } from '@nestjs/testing'
import { PensionCorrectionsResolver } from './pension-corrections.resolver'
import { PensionCorrectionsService } from './pension-corrections.service'

describe('PensionCorrectionsResolver', () => {
    let resolver: PensionCorrectionsResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PensionCorrectionsResolver, PensionCorrectionsService],
        }).compile()

        resolver = module.get<PensionCorrectionsResolver>(
            PensionCorrectionsResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

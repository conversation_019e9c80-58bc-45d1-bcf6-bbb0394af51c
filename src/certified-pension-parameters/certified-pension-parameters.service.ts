import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CertifiedPensionParameters } from '../certified-data/entities/certified-pension-parameters.entity'
import { CreateCertifiedPensionParametersInput } from '../certified-data/dto/create-certified-pension-parameters.input'

@Injectable()
export class CertifiedPensionParametersService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedParameters =
            await this.prisma.certifiedPensionParameters.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedParameters) {
            throw new NotFoundException(
                `CertifiedPensionParameters with ID ${id} not found`
            )
        }

        return certifiedParameters
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedParameters =
            await this.prisma.certifiedPensionParameters.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedParameters) {
            throw new NotFoundException(
                `CertifiedPensionParameters for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedParameters
    }

    async create(
        createCertifiedPensionParametersInput: CreateCertifiedPensionParametersInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...parametersData } =
            createCertifiedPensionParametersInput

        return this.prisma.certifiedPensionParameters.create({
            data: {
                ...parametersData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        // Convert to number if the value is numeric
        const processedValue = !isNaN(Number(newValue))
            ? Number(newValue)
            : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo = await this.prisma.certifiedPensionParameters.update(
            {
                where: { id: entityId },
                data: updateData,
                include: {
                    certifiedData: true,
                },
            }
        )

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedPensionParameters with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedPensionParameters.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedPensionParameters with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo = await this.prisma.certifiedPensionParameters.update(
            {
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            }
        )

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate =
            await this.prisma.certifiedPensionParameters.findUnique({
                where: { id },
            })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedPensionParameters with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPensionParameters.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

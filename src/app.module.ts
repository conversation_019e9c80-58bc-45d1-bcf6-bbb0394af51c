import {
    Module,
    NestModule,
    MiddlewareConsumer,
    RequestMethod,
} from '@nestjs/common'
import { JsonBodyMiddleware } from '@golevelup/nestjs-webhooks'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { GraphQLModule } from '@nestjs/graphql'
import { ApolloDriver } from '@nestjs/apollo'
import { join } from 'path'
import { ConfigModule, ConfigService } from '@nestjs/config'
import configs from './config'
import { RawBodyMiddleware } from './middlewares/raw-body.middleware'
import { MailerModule } from '@nestjs-modules/mailer'
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter'
import { Context } from 'graphql-ws'
import { admin } from './auth/firebase-admin.module'
import { ScheduleModule } from '@nestjs/schedule'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { UserModule } from './user/user.module'
import { SystemSettingsModule } from './system-settings/system-settings.module'
import { PrismaModule } from './prisma.module'
import { ParticipantModule } from './participant/participant.module'
import { PersonalInfoModule } from './personal-info/personal-info.module'
import { DocumentsModule } from './documents/documents.module'
import { EmploymentInfoModule } from './employment-info/employment-info.module'
import { PensionInfoModule } from './pension-info/pension-info.module'
import { ChangeProposalModule } from './change-proposal/change-proposal.module'
import { CertifiedDataModule } from './certified-data/certified-data.module'
import { AddressModule } from './address/address.module'
import { PensionCorrectionsModule } from './pension-corrections/pension-corrections.module'
import { PensionParametersModule } from './pension-parameters/pension-parameters.module'
import { ChangeDataModule } from './change-data/change-data.module'
import { ChildModule } from './child/child.module'
import { SalaryEntryModule } from './salary-entry/salary-entry.module'
import { AuditLogModule } from './audit-log/audit-log.module'
import { RoleModule } from './role/role.module'
import { EmailHandlerModule } from './email-handler/email-handler.module'
import { NotificationsModule } from './notifications/notifications.module'
import { PubSubModule } from './pub-sub/pub-sub.module'
import { PartnerInfoModule } from './partner-info/partner-info.module'
import { CertifiedPensionInfoModule } from './certified-pension-info/certified-pension-info.module'
import { CertifiedEmploymentInfoModule } from './certified-employment-info/certified-employment-info.module'
import { CertifiedPersonalInfoModule } from './certified-personal-info/certified-personal-info.module'
import { CertifiedIndexationStartOfYearModule } from './certified-indexation-start-of-year/certified-indexation-start-of-year.module'
import { CertifiedPensionCorrectionsModule } from './certified-pension-corrections/certified-pension-corrections.module'
import { CertifiedPensionCorrectionsStartOfYearModule } from './certified-pension-corrections-start-of-year/certified-pension-corrections-start-of-year.module'
import { CertifiedPensionCorrectionsEndOfYearModule } from './certified-pension-corrections-end-of-year/certified-pension-corrections-end-of-year.module'
import { CertifiedPensionsAccrualModule } from './certified-pensions-accrual/certified-pensions-accrual.module'
import { CertifiedVoluntaryContributionsModule } from './certified-voluntary-contributions/certified-voluntary-contributions.module'
import { CertifiedPensionParametersModule } from './certified-pension-parameters/certified-pension-parameters.module'
import { CertificationRejectReasonModule } from './certification-reject-reason/certification-reject-reason.module'
import { CertifiedChildModule } from './certified-child/certified-child.module'
import { CertifiedPartnerInfoModule } from './certified-partner-info/certified-partner-info.module'
import { CertifiedAddressModule } from './certified-address/certified-address.module'
import { CommonModule } from './common/common.module'
import { PensionCalculationsModule } from './pension-calculations/pension-calculations.module'

@Module({
    imports: [
        GraphQLModule.forRootAsync({
            imports: [ConfigModule, AppModule],
            inject: [ConfigService],
            driver: ApolloDriver,
            useFactory: async (configService: ConfigService) => {
                return {
                    installSubscriptionHandlers: true,
                    playground: configService.get('NODE_ENV') !== 'production',
                    autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
                    sortSchema: true,
                    introspection: true,
                    subscriptions: {
                        'graphql-ws': {
                            onConnect: async (context: Context<any, any>) => {
                                const { connectionParams } = context
                                let tokenObj: Readonly<any>
                                if (typeof connectionParams === 'string') {
                                    tokenObj = JSON.parse(connectionParams)
                                } else {
                                    tokenObj = connectionParams
                                }
                                const user = await admin
                                    .auth()
                                    .verifyIdToken(tokenObj.token)

                                context.extra.user = user

                                return true
                            },
                        },
                        'subscriptions-transport-ws': {
                            keepalive: 20000,
                            onConnect: async (connectionParams: any) => {
                                let tokenObj = null

                                if (typeof connectionParams === 'string') {
                                    tokenObj = JSON.parse(connectionParams)
                                } else {
                                    tokenObj = connectionParams
                                }

                                if (!tokenObj?.token) {
                                    throw new Error('Token not provided')
                                }

                                const user = await admin
                                    .auth()
                                    .verifyIdToken(tokenObj.token)

                                if (!user) {
                                    throw new Error('Invalid token')
                                }
                                return { user }
                            },
                        },
                    },
                    context: ({ req, res, connection }) => {
                        if (connection) {
                            return {
                                req,
                                res,
                                user: connection.context.extra.user,
                            }
                        }
                        return { req, res }
                    },
                }
            },
        }),
        MailerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    transport: {
                        host: configService.get('MAIL_HOST'),
                        port: configService.get('MAIL_PORT'),
                        secure: true, //TODO: Change to true if using SSL
                        auth: {
                            user: configService.get('MAIL_USER'),
                            pass: configService.get('MAIL_PASS'),
                        },
                    },
                    defaults: {
                        from: `"No Reply"`,
                    },
                    template: {
                        dir: join(__dirname, '../templates'),
                        adapter: new HandlebarsAdapter(),
                        options: {
                            strict: true,
                        },
                    },
                }
            },
        }),
        ConfigModule.forRoot({ cache: true, isGlobal: true, load: [configs] }),
        ScheduleModule.forRoot(),
        UserModule,
        PrismaModule,
        EventEmitterModule.forRoot(),
        SystemSettingsModule,
        ParticipantModule,
        PersonalInfoModule,
        DocumentsModule,
        EmploymentInfoModule,
        PensionInfoModule,
        ChangeProposalModule,
        CertifiedDataModule,
        CertifiedPensionInfoModule,
        CertifiedEmploymentInfoModule,
        CertifiedPersonalInfoModule,
        CertifiedIndexationStartOfYearModule,
        CertifiedPensionCorrectionsModule,
        CertifiedPensionCorrectionsStartOfYearModule,
        CertifiedPensionCorrectionsEndOfYearModule,
        CertifiedPensionsAccrualModule,
        CertifiedVoluntaryContributionsModule,
        CertifiedPensionParametersModule,
        CertificationRejectReasonModule,
        AddressModule,
        PensionCorrectionsModule,
        PensionParametersModule,
        ChangeDataModule,
        ChildModule,
        SalaryEntryModule,
        AuditLogModule,
        RoleModule,
        EmailHandlerModule,
        NotificationsModule,
        PubSubModule,
        PartnerInfoModule,
        CertifiedChildModule,
        CertifiedPartnerInfoModule,
        CertifiedAddressModule,
        CommonModule,
        PensionCalculationsModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(RawBodyMiddleware)
            .forRoutes({
                path: '/subscription/handle-webhook',
                method: RequestMethod.POST,
            })
            .apply(JsonBodyMiddleware)
            .forRoutes('*')
    }
}

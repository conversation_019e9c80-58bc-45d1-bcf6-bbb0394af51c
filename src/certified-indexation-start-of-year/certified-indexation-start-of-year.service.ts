import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertifiedIndexationStartOfYearInput } from '../certified-data/dto'

@Injectable()
export class CertifiedIndexationStartOfYearService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedIndexation =
            await this.prisma.certifiedIndexationStartOfYear.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedIndexation) {
            throw new NotFoundException(
                `CertifiedIndexationStartOfYear with ID ${id} not found`
            )
        }

        return certifiedIndexation
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedIndexation =
            await this.prisma.certifiedIndexationStartOfYear.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedIndexation) {
            throw new NotFoundException(
                `CertifiedIndexationStartOfYear for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedIndexation
    }

    async create(
        createCertifiedIndexationStartOfYearInput: CreateCertifiedIndexationStartOfYearInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...indexationData } =
            createCertifiedIndexationStartOfYearInput

        return this.prisma.certifiedIndexationStartOfYear.create({
            data: {
                ...indexationData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        // Convert to number if the value is numeric
        const processedValue = !isNaN(Number(newValue))
            ? Number(newValue)
            : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo =
            await this.prisma.certifiedIndexationStartOfYear.update({
                where: { id: entityId },
                data: updateData,
                include: {
                    certifiedData: true,
                },
            })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedIndexationStartOfYear with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedIndexationStartOfYear.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedIndexationStartOfYear with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo =
            await this.prisma.certifiedIndexationStartOfYear.update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate =
            await this.prisma.certifiedIndexationStartOfYear.findUnique({
                where: { id },
            })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedIndexationStartOfYear with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedIndexationStartOfYear.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

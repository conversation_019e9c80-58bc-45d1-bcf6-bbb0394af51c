import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertificationRejectReasonInput } from './dto/create-certification-reject-reason.input'
import { CertificationRejectReasonStatus } from '@prisma/client'

@Injectable()
export class CertificationRejectReasonService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createCertificationRejectReasonInput: CreateCertificationRejectReasonInput
    ) {
        return this.prisma.certificationRejectReason.create({
            data: createCertificationRejectReasonInput,
        })
    }

    async findAll() {
        return this.prisma.certificationRejectReason.findMany({
            include: {
                certifiedData: true,
                certifiedPensionInfo: true,
                certifiedEmploymentInfo: true,
                certifiedPersonalInfo: true,
                certifiedIndexationStartOfYear: true,
                certifiedPensionCorrections: true,
                certifiedVoluntaryContributions: true,
                certifiedPensionParameters: true,
            },
        })
    }

    async findOne(id: string) {
        const certificationRejectReason =
            await this.prisma.certificationRejectReason.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                    certifiedPensionInfo: true,
                    certifiedEmploymentInfo: true,
                    certifiedPersonalInfo: true,
                    certifiedIndexationStartOfYear: true,
                    certifiedPensionCorrections: true,
                    certifiedVoluntaryContributions: true,
                    certifiedPensionParameters: true,
                },
            })

        if (!certificationRejectReason) {
            throw new NotFoundException(
                `CertificationRejectReason with ID "${id}" not found`
            )
        }

        return certificationRejectReason
    }

    async remove(id: string) {
        const certificationRejectReason = await this.findOne(id)

        await this.prisma.certificationRejectReason.delete({
            where: { id },
        })

        return { id, deleted: true }
    }

    /**
     * Invalidate reject reasons for a specific field in an entity
     * This is typically called when a field is updated after rejection
     */
    async invalidateRejectReasonsForField(
        entityId: string,
        field: string,
        entityType: string
    ) {
        const whereClause: any = {
            field,
            status: CertificationRejectReasonStatus.VALID,
        }

        // Set the appropriate foreign key based on entity type
        switch (entityType) {
            case 'certifiedData':
                whereClause.certifiedDataId = entityId
                break
            case 'certifiedPensionInfo':
                whereClause.certifiedPensionInfoId = entityId
                break
            case 'certifiedEmploymentInfo':
                whereClause.certifiedEmploymentInfoId = entityId
                break
            case 'certifiedPersonalInfo':
                whereClause.certifiedPersonalInfoId = entityId
                break
            case 'certifiedIndexationStartOfYear':
                whereClause.certifiedIndexationStartOfYearId = entityId
                break
            case 'certifiedPensionCorrections':
                whereClause.certifiedPensionCorrectionsId = entityId
                break
            case 'certifiedVoluntaryContributions':
                whereClause.certifiedVoluntaryContributionsId = entityId
                break
            case 'certifiedPensionParameters':
                whereClause.certifiedPensionParametersId = entityId
                break
            case 'certifiedSalaryEntry':
                whereClause.certifiedSalaryEntryId = entityId
                break
        }

        // Update all matching reject reasons to INVALID status
        const result = await this.prisma.certificationRejectReason.updateMany({
            where: whereClause,
            data: {
                status: CertificationRejectReasonStatus.INVALID,
            },
        })

        return result
    }
}

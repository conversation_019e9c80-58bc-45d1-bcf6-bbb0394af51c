import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { CertificationRejectReasonService } from './certification-reject-reason.service'
import { CertificationRejectReason } from './entities/certification-reject-reason.entity'
import { CreateCertificationRejectReasonInput } from './dto/create-certification-reject-reason.input'

@Resolver(() => CertificationRejectReason)
export class CertificationRejectReasonResolver {
    constructor(
        private readonly certificationRejectReasonService: CertificationRejectReasonService
    ) {}

    @Mutation(() => CertificationRejectReason)
    createCertificationRejectReason(
        @Args('createCertificationRejectReasonInput')
        createCertificationRejectReasonInput: CreateCertificationRejectReasonInput
    ) {
        return this.certificationRejectReasonService.create(
            createCertificationRejectReasonInput
        )
    }

    @Query(() => [CertificationRejectReason], {
        name: 'certificationRejectReasons',
    })
    findAll() {
        return this.certificationRejectReasonService.findAll()
    }

    @Query(() => CertificationRejectReason, {
        name: 'certificationRejectReason',
    })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.certificationRejectReasonService.findOne(id)
    }

    @Mutation(() => CertificationRejectReason)
    removeCertificationRejectReason(
        @Args('id', { type: () => String }) id: string
    ) {
        return this.certificationRejectReasonService.remove(id)
    }
}

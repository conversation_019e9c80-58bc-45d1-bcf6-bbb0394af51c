import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionInfoService } from './certified-pension-info.service'
import { CertifiedPensionInfo } from '../certified-data/entities/certified-pension-info.entity'
import { CreateCertifiedPensionInfoInput } from '../certified-data/dto/create-certified-pension-info.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionInfo)
export class CertifiedPensionInfoResolver {
    constructor(
        private readonly certifiedPensionInfoService: CertifiedPensionInfoService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionInfo)
    async certifiedPensionInfo(
        @Args('id') id: string
    ): Promise<CertifiedPensionInfo> {
        return this.certifiedPensionInfoService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionInfo)
    async certifiedPensionInfoByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ): Promise<CertifiedPensionInfo> {
        return this.certifiedPensionInfoService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionInfo)
    async createCertifiedPensionInfo(
        @Args('createCertifiedPensionInfoInput')
        createCertifiedPensionInfoInput: CreateCertifiedPensionInfoInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ): Promise<CertifiedPensionInfo> {
        return this.certifiedPensionInfoService.create(
            createCertifiedPensionInfoInput,
            certifiedDataId
        )
    }
}

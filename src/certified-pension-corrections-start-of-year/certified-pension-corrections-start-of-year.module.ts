import { Module } from '@nestjs/common'
import { CertifiedPensionCorrectionsStartOfYearService } from './certified-pension-corrections-start-of-year.service'
import { CertifiedPensionCorrectionsStartOfYearResolver } from './certified-pension-corrections-start-of-year.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedPensionCorrectionsStartOfYearResolver,
        CertifiedPensionCorrectionsStartOfYearService,
    ],
    exports: [CertifiedPensionCorrectionsStartOfYearService],
})
export class CertifiedPensionCorrectionsStartOfYearModule {}

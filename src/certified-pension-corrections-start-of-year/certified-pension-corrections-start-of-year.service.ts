import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertifiedPensionCorrectionsStartOfYearInput } from '../certified-data/dto/create-certified-pension-corrections-start-of-year.input'

@Injectable()
export class CertifiedPensionCorrectionsStartOfYearService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedCorrections =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedCorrections) {
            throw new NotFoundException(
                `CertifiedPensionCorrectionsStartOfYear with ID ${id} not found`
            )
        }

        return certifiedCorrections
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedCorrections =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedCorrections) {
            throw new NotFoundException(
                `CertifiedPensionCorrectionsStartOfYear for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedCorrections
    }

    async create(
        createCertifiedPensionCorrectionsStartOfYearInput: CreateCertifiedPensionCorrectionsStartOfYearInput,
        certifiedDataId: string
    ) {
        const { certificationRejectReason, ...correctionsData } =
            createCertifiedPensionCorrectionsStartOfYearInput as any

        return this.prisma.certifiedPensionCorrectionsStartOfYear.create({
            data: {
                ...correctionsData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const processedValue = !isNaN(Number(newValue))
            ? Number(newValue)
            : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.update({
                where: { id: entityId },
                data: updateData,
                include: {
                    certifiedData: true,
                },
            })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedPensionCorrectionsStartOfYear with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedPensionCorrectionsStartOfYear with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate =
            await this.prisma.certifiedPensionCorrectionsStartOfYear.findUnique({
                where: { id },
            })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedPensionCorrectionsStartOfYear with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPensionCorrectionsStartOfYear.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

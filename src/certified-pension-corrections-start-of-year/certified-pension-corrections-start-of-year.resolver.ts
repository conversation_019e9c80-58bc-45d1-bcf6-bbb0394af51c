import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionCorrectionsStartOfYearService } from './certified-pension-corrections-start-of-year.service'
import { CertifiedPensionCorrectionsStartOfYear } from '../certified-data/entities/certified-pension-corrections-start-of-year.entity'
import { CreateCertifiedPensionCorrectionsStartOfYearInput } from '../certified-data/dto/create-certified-pension-corrections-start-of-year.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionCorrectionsStartOfYear)
export class CertifiedPensionCorrectionsStartOfYearResolver {
    constructor(
        private readonly certifiedCorrectionsStartService: CertifiedPensionCorrectionsStartOfYearService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrectionsStartOfYear)
    async certifiedPensionCorrectionsStartOfYear(@Args('id') id: string) {
        return this.certifiedCorrectionsStartService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrectionsStartOfYear)
    async certifiedPensionCorrectionsStartOfYearByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedCorrectionsStartService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionCorrectionsStartOfYear)
    async createCertifiedPensionCorrectionsStartOfYear(
        @Args('createCertifiedPensionCorrectionsStartOfYearInput')
        createCertifiedPensionCorrectionsStartOfYearInput: CreateCertifiedPensionCorrectionsStartOfYearInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedCorrectionsStartService.create(
            createCertifiedPensionCorrectionsStartOfYearInput,
            certifiedDataId
        )
    }
}

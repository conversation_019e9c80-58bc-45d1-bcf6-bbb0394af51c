import { Test, TestingModule } from '@nestjs/testing'
import { ChangeDataResolver } from './change-data.resolver'
import { ChangeDataService } from './change-data.service'

describe('ChangeDataResolver', () => {
    let resolver: ChangeDataResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [ChangeDataResolver, ChangeDataService],
        }).compile()

        resolver = module.get<ChangeDataResolver>(ChangeDataResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

import { Test, TestingModule } from '@nestjs/testing'
import { ChangeDataService } from './change-data.service'

describe('ChangeDataService', () => {
    let service: ChangeDataService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [ChangeDataService],
        }).compile()

        service = module.get<ChangeDataService>(ChangeDataService)
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})

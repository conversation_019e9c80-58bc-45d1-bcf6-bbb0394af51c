import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import * as Pusher from 'pusher'

export interface PusherMessage {
    event: string
    data: any
    channel?: string
}

@Injectable()
export class PusherService {
    private readonly logger = new Logger(PusherService.name)
    private pusher: Pusher

    constructor(private configService: ConfigService) {
        this.pusher = new Pusher({
            appId: this.configService.get('PUSHER_APP_ID'),
            key: this.configService.get('PUSHER_KEY'),
            secret: this.configService.get('PUSHER_SECRET'),
            cluster: this.configService.get('PUSHER_CLUSTER'),
            useTLS: true,
        })
    }

    async publishToChannel(
        channel: string,
        event: string,
        data: any
    ): Promise<void> {
        try {
            await this.pusher.trigger(channel, event, data)
            this.logger.log(
                `Message published to channel: ${channel}, event: ${event}`
            )
        } catch (error) {
            this.logger.error(`Failed to publish to channel ${channel}:`, error)
            throw error
        }
    }

    async publishToMultipleChannels(
        channels: string[],
        event: string,
        data: any
    ): Promise<void> {
        try {
            await this.pusher.trigger(channels, event, data)
            this.logger.log(
                `Message published to channels: ${channels.join(
                    ', '
                )}, event: ${event}`
            )
        } catch (error) {
            this.logger.error(
                `Failed to publish to channels ${channels.join(', ')}:`,
                error
            )
            throw error
        }
    }

    async publishToUserChannel(
        userId: string,
        event: string,
        data: any
    ): Promise<void> {
        const channel = `user-${userId}`
        await this.publishToChannel(channel, event, data)
    }

    async publishBatch(messages: PusherMessage[]): Promise<void> {
        try {
            const batch = messages.map((msg) => ({
                channel: msg.channel || 'default',
                name: msg.event,
                data: msg.data,
            }))

            await this.pusher.triggerBatch(batch)
            this.logger.log(`Batch of ${messages.length} messages published`)
        } catch (error) {
            this.logger.error('Failed to publish batch messages:', error)
            throw error
        }
    }
}

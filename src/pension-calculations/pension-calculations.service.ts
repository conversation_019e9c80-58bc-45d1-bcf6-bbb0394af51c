import { Injectable } from '@nestjs/common'
import {
  CalculatePensionBaseDto,
  CalculateOpTeAccrualToReferenceDateDto,
  CalculateWpTeAccrualToReferenceDateDto,
  CalculateOpTeAccrualAfterReferenceDateDto,
  CalculateRetirementDateDto,
  CalculateAccrualPeriodToReferenceDateDto,
  CalculateAccrualPeriodAfterReferenceDateDto,
  CalculateCertifiedDataDto,
  GetCalculatedDataByTypeDto,
  CertifiedPensionInfoResponse,
  CertifiedIndexationStartOfYearResponse,
  CertifiedPensionCorrectionsResponse,
  CertifiedEmploymentInfoResponse,
  CertifiedPensionParametersResponse,
  CertifiedVoluntaryContributionsResponse,
} from './dto/pension-calculations.dto'
import { days360 } from '../utils/date.utils'

@Injectable()
export class PensionCalculationsService {
    /**
     * Calculates the pension base from gross fulltime annual salary
     * If gross fulltime annual salary minus offset is less than minimum pension base,
     * then the pension base should be set to the minimum pension base
     *
     * @param grossFulltimeAnnualSalary - The gross fulltime annual salary
     * @param offsetAmount - The offset amount to deduct from salary
     * @param minimumPensionBase - The minimum pension base threshold
     * @returns The calculated pension base
     */
    calculatePensionBase(
        grossFulltimeAnnualSalary: number,
        offsetAmount: number,
        minimumPensionBase: number
    ): number {
        // Calculate pension base: gross salary minus offset
        const calculatedPensionBase = grossFulltimeAnnualSalary - offsetAmount

        // Apply minimum pension base rule
        return Math.max(calculatedPensionBase, minimumPensionBase)
    }

    /**
     * Calculates the fulltime annual salary from monthly salary
     *
     * @param monthlySalary - The monthly salary amount
     * @param annualMultiplier - The annual multiplier (e.g., 13 for 13th month)
     * @returns The calculated fulltime annual salary
     */
    calculateFulltimeAnnualSalary(
        monthlySalary: number,
        annualMultiplier: number
    ): number {
        return monthlySalary * annualMultiplier
    }

    calculateOpTeAccrualToReferenceDate(
        {
            pensionBase,
            accrualPercentage,
            startDate,
            referenceYear,
            parttimePercentage,
            referenceDate
        }: CalculateOpTeAccrualToReferenceDateDto
    ): number | null {
        if (!pensionBase || !accrualPercentage || !startDate) return null

        const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 }
        const periodStartDate = new Date(referenceYear, 0, 1)
        const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day)
        const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate

        const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day)

        const accrualPeriod = days360(
            calculationEndDate.getFullYear(),
            calculationEndDate.getMonth() + 1,
            calculationEndDate.getDate(),
            calculationStartDate.getFullYear(),
            calculationStartDate.getMonth() + 1,
            calculationStartDate.getDate(),
        ) / 360

        let pension = pensionBase * accrualPercentage * accrualPeriod

        if (parttimePercentage !== undefined) {
            pension *= parttimePercentage
        }

        return pension
    }

    calculateWpTeAccrualToReferenceDate(
        {
            pensionBase,
            accrualPercentage,
            partnersPensionPercentage,
            startDate,
            referenceYear,
            parttimePercentage,
            referenceDate
        }: CalculateWpTeAccrualToReferenceDateDto
    ): number | null {
        if (!pensionBase || !accrualPercentage || !startDate || !partnersPensionPercentage) return null

        const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 }

        const periodStartDate = new Date(referenceYear, 0, 1)
        const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day)
        const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate

        const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day)

        const accrualPeriod = days360(
            calculationEndDate.getFullYear(),
            calculationEndDate.getMonth() + 1,
            calculationEndDate.getDate(),
            calculationStartDate.getFullYear(),
            calculationStartDate.getMonth() + 1,
            calculationStartDate.getDate(),
        ) / 360

        let basePension = pensionBase * accrualPercentage * accrualPeriod

        if (parttimePercentage !== undefined) {
            basePension *= parttimePercentage
        }

        return basePension * partnersPensionPercentage
    }

    calculateOpTeAccrualAfterReferenceDate(
        {
            dateOfBirth,
            retirementAge,
            referenceDate,
            accrualPercentage,
            parttimePercentage,
            pensionBase
        }: CalculateOpTeAccrualAfterReferenceDateDto
    ): number | null {
        if (!dateOfBirth || !retirementAge || !accrualPercentage) return null

        const calculationStartDate = referenceDate instanceof Date
            ? referenceDate
            : new Date(referenceDate.year, referenceDate.month - 1, referenceDate.day)

        const calculationEndDate = this.calculateRetirementDate({
            dateOfBirth,
            retirementAge
        })

        if (calculationEndDate <= calculationStartDate) return 0

        const accrualPeriod = days360(
            calculationEndDate.getFullYear(),
            calculationEndDate.getMonth() + 1,
            calculationEndDate.getDate(),
            calculationStartDate.getFullYear(),
            calculationStartDate.getMonth() + 1,
            calculationStartDate.getDate(),
        ) / 360

        let futurePension = pensionBase ? pensionBase * accrualPercentage * accrualPeriod : 0

        if (parttimePercentage !== undefined) {
            futurePension *= parttimePercentage
        }

        return futurePension
    }

    calculateRetirementDate(
        { dateOfBirth, retirementAge }: CalculateRetirementDateDto
    ): Date {
        const wholeYears = Math.floor(retirementAge)
        const fractionalYears = retirementAge - wholeYears

        const additionalMonths = Math.round(fractionalYears * 12)

        return new Date(
            dateOfBirth.year + wholeYears,
            dateOfBirth.month - 1 + additionalMonths,
            dateOfBirth.day,
        )
    }

    calculateAccrualPeriodToReferenceDate(
        { startDate, referenceYear, referenceDate }: CalculateAccrualPeriodToReferenceDateDto
    ): number | null {
        if (!startDate) return null

        const endDate = referenceDate || { year: referenceYear, month: 12, day: 31 }

        const periodStartDate = new Date(referenceYear, 0, 1)
        const employmentStartDate = new Date(startDate.year, startDate.month - 1, startDate.day)
        const calculationStartDate = employmentStartDate < periodStartDate ? periodStartDate : employmentStartDate

        const calculationEndDate = new Date(endDate.year, endDate.month - 1, endDate.day)

        return days360(
            calculationEndDate.getFullYear(),
            calculationEndDate.getMonth() + 1,
            calculationEndDate.getDate(),
            calculationStartDate.getFullYear(),
            calculationStartDate.getMonth() + 1,
            calculationStartDate.getDate(),
        ) / 360
    }

    calculateAccrualPeriodAfterReferenceDate(
        { dateOfBirth, retirementAge, referenceDate }: CalculateAccrualPeriodAfterReferenceDateDto
    ): number | null {
        if (!dateOfBirth || !retirementAge) return null

        const calculationStartDate = referenceDate instanceof Date
            ? referenceDate
            : new Date(referenceDate.year, referenceDate.month - 1, referenceDate.day)

        const calculationEndDate = this.calculateRetirementDate({
            dateOfBirth,
            retirementAge
        })

        if (calculationEndDate <= calculationStartDate) return 0

        return days360(
            calculationEndDate.getFullYear(),
            calculationEndDate.getMonth() + 1,
            calculationEndDate.getDate(),
            calculationStartDate.getFullYear(),
            calculationStartDate.getMonth() + 1,
            calculationStartDate.getDate(),
        ) / 360
    }

    calculateCertifiedPensionInfo(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedPensionInfoResponse {
        const baseAmount = 10000
        const yearFactor = (year - 2020) * 0.02

        return {
            __typename: 'CertifiedPensionInfo',
            id: `calc-pension-info-${year}`,
            code: 10 + (year - 2020),
            codeDescription: `Calculated pension info for ${year}`,
            accruedGrossAnnualOldAgePension: baseAmount * (1 + yearFactor),
            accruedGrossAnnualPartnersPension: baseAmount * 0.7 * (1 + yearFactor),
            accruedGrossAnnualSinglesPension: baseAmount * 0.5 * (1 + yearFactor),
            attainableGrossAnnualOldAgePension: baseAmount * 1.5 * (1 + yearFactor),
            grossAnnualDisabilityPension: baseAmount * 0.8 * (1 + yearFactor),
        }
    }

    calculateCertifiedIndexationStartOfYear(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedIndexationStartOfYearResponse {
        const baseAmount = 10000
        const indexationRate = 0.025
        const yearsSince2020 = year - 2020

        return {
            __typename: 'CertifiedIndexationStartOfYear',
            id: `calc-indexation-${year}`,
            accruedGrossAnnualOldAgePension: baseAmount * Math.pow(1 + indexationRate, yearsSince2020),
            accruedGrossAnnualPartnersPension: baseAmount * 0.7 * Math.pow(1 + indexationRate, yearsSince2020),
            accruedGrossAnnualSinglesPension: baseAmount * 0.5 * Math.pow(1 + indexationRate, yearsSince2020),
            extraAccruedGrossAnnualOldAgePension: baseAmount * 0.1 * Math.pow(1 + indexationRate, yearsSince2020),
            extraAccruedGrossAnnualPartnersPension: baseAmount * 0.07 * Math.pow(1 + indexationRate, yearsSince2020),
            grossAnnualDisabilityPension: baseAmount * 0.8 * Math.pow(1 + indexationRate, yearsSince2020),
        }
    }

    calculateCertifiedPensionCorrectionsStartOfYear(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedPensionCorrectionsResponse {
        const baseCorrection = 100
        const yearMultiplier = year - 2019

        return {
            __typename: 'CertifiedPensionCorrectionsStartOfYear',
            id: `calc-corrections-start-${year}`,
            accruedGrossAnnualOldAgePension: baseCorrection * yearMultiplier,
            accruedGrossAnnualPartnersPension: baseCorrection * yearMultiplier * 0.7,
            accruedGrossAnnualSinglesPension: baseCorrection * yearMultiplier * 0.5,
            attainableGrossAnnualOldAgePension: baseCorrection * yearMultiplier * 1.5,
            extraAccruedGrossAnnualOldAgePension: baseCorrection * yearMultiplier * 0.1,
            extraAccruedGrossAnnualPartnersPension: baseCorrection * yearMultiplier * 0.07,
            grossAnnualDisabilityPension: baseCorrection * yearMultiplier * 0.8,
            pendingChanges: [],
            requestedChanges: [],
            approvedChanges: [],
        }
    }

    calculateCertifiedPensionCorrectionsEndOfYear(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedPensionCorrectionsResponse {
        const baseCorrection = 200
        const yearMultiplier = year - 2019

        return {
            __typename: 'CertifiedPensionCorrectionsEndOfYear',
            id: `calc-corrections-end-${year}`,
            accruedGrossAnnualOldAgePension: baseCorrection * yearMultiplier,
            accruedGrossAnnualPartnersPension: baseCorrection * yearMultiplier * 0.7,
            accruedGrossAnnualSinglesPension: baseCorrection * yearMultiplier * 0.5,
            attainableGrossAnnualOldAgePension: baseCorrection * yearMultiplier * 1.5,
            extraAccruedGrossAnnualOldAgePension: baseCorrection * yearMultiplier * 0.1,
            extraAccruedGrossAnnualPartnersPension: baseCorrection * yearMultiplier * 0.07,
            grossAnnualDisabilityPension: baseCorrection * yearMultiplier * 0.8,
            pendingChanges: [],
            requestedChanges: [],
            approvedChanges: [],
        }
    }

    calculateCertifiedPensionCorrections(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedPensionCorrectionsResponse {
        const baseCorrectionAmount = 500
        const yearFactor = (year - 2020) * 0.03

        return {
            __typename: 'CertifiedPensionCorrections',
            id: `calc-pension-corrections-${year}`,
            year: String(year),
            accruedGrossAnnualOldAgePension: baseCorrectionAmount * (1 + yearFactor),
            attainableGrossAnnualOldAgePension: baseCorrectionAmount * 1.2 * (1 + yearFactor),
            accruedGrossAnnualPartnersPension: baseCorrectionAmount * 0.7 * (1 + yearFactor),
            accruedGrossAnnualSinglesPension: baseCorrectionAmount * 0.5 * (1 + yearFactor),
            grossAnnualDisabilityPension: baseCorrectionAmount * 0.8 * (1 + yearFactor),
            extraAccruedGrossAnnualOldAgePension: baseCorrectionAmount * 0.15 * (1 + yearFactor),
            extraAccruedGrossAnnualPartnersPension: baseCorrectionAmount * 0.1 * (1 + yearFactor),
            correction: baseCorrectionAmount * 2 * (1 + yearFactor),
            pendingChanges: [],
            requestedChanges: [],
            approvedChanges: [],
        }
    }

    calculateCertifiedEmploymentInfo(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedEmploymentInfoResponse {
        const employmentInfo = participantData?.employmentInfo || {}

        return {
            __typename: 'CertifiedEmploymentInfo',
            id: `calc-employment-${year}`,
            department: employmentInfo.department || 'Calculated Department',
            employeeId: employmentInfo.employeeId || `EMP-${year}`,
            havNum: employmentInfo.havNum || 1,
            position: employmentInfo.position || 'Calculated Position',
            regNum: employmentInfo.regNum || 1000 + year,
            startDate: employmentInfo.startDate || new Date(year - 10, 0, 1).toISOString(),
            status: employmentInfo.status || 'Active',
            endDate: employmentInfo.endDate || null,
        }
    }

    calculateCertifiedPensionParameters(
        { year, participantData }: CalculateCertifiedDataDto
    ): CertifiedPensionParametersResponse {
        return {
            __typename: 'CertifiedPensionParameters',
            id: `calc-parameters-${year}`,
            year: String(year),
            accrualPercentage: 0.0175,
            annualMultiplier: 13.96,
            offsetAmount: 15883,
            minimumPensionBase: 0,
            partnersPensionPercentage: 0.7,
            retirementAge: 65,
        }
    }

    getCalculatedDataByType(
        { year, dataType, participantData }: GetCalculatedDataByTypeDto
    ): any {
        switch (dataType) {
            case 'certifiedPensionInfo':
                return this.calculateCertifiedPensionInfo({ year, participantData })
            case 'certifiedIndexationStartOfYear':
                return this.calculateCertifiedIndexationStartOfYear({ year, participantData })
            case 'certifiedPensionCorrectionsStartOfYear':
                return this.calculateCertifiedPensionCorrectionsStartOfYear({ year, participantData })
            case 'certifiedPensionCorrectionsEndOfYear':
                return this.calculateCertifiedPensionCorrectionsEndOfYear({ year, participantData })
            case 'certifiedEmploymentInfo':
                return this.calculateCertifiedEmploymentInfo({ year, participantData })
            case 'certifiedPensionParameters':
                return this.calculateCertifiedPensionParameters({ year, participantData })
            case 'certifiedPensionCorrections':
                return this.calculateCertifiedPensionCorrections({ year, participantData })
            case 'certifiedPersonalInfo':
                return participantData?.personalInfo || null
            case 'certifiedVoluntaryContributions':
                return {
                    __typename: 'CertifiedVoluntaryContributions',
                    id: `calc-voluntary-${year}`,
                    amount: 0,
                }
            default:
                return null
        }
    }
}

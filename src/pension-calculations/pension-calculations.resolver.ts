import { Resolver, Query, Args, Float, Int } from '@nestjs/graphql'
import { PensionCalculationsService } from './pension-calculations.service'
import { GraphQLJSON } from 'graphql-type-json'
import {
    CalculateOpTeAccrualToReferenceDateInput,
    CalculateWpTeAccrualToReferenceDateInput,
    CalculateOpTeAccrualAfterReferenceDateInput,
    CalculateRetirementDateInput,
    CalculateAccrualPeriodToReferenceDateInput,
    CalculateAccrualPeriodAfterReferenceDateInput,
} from './dto/pension-calculations.input'

@Resolver()
export class PensionCalculationsResolver {
    constructor(
        private readonly pensionCalculationsService: PensionCalculationsService
    ) {}

    @Query(() => Float, { name: 'calculatePensionBase' })
    calculatePensionBase(
        @Args('grossFulltimeAnnualSalary', { type: () => Float }) grossFulltimeAnnualSalary: number,
        @Args('offsetAmount', { type: () => Float }) offsetAmount: number,
        @Args('minimumPensionBase', { type: () => Float }) minimumPensionBase: number
    ): number {
        return this.pensionCalculationsService.calculatePensionBase(
            grossFulltimeAnnualSalary,
            offsetAmount,
            minimumPensionBase
        )
    }

    @Query(() => Float, { nullable: true, name: 'calculateOpTeAccrualToReferenceDate' })
    calculateOpTeAccrualToReferenceDate(
        @Args('input') input: CalculateOpTeAccrualToReferenceDateInput
    ): number | null {
        return this.pensionCalculationsService.calculateOpTeAccrualToReferenceDate(input)
    }

    @Query(() => Float, { nullable: true, name: 'calculateWpTeAccrualToReferenceDate' })
    calculateWpTeAccrualToReferenceDate(
        @Args('input') input: CalculateWpTeAccrualToReferenceDateInput
    ): number | null {
        return this.pensionCalculationsService.calculateWpTeAccrualToReferenceDate(input)
    }

    @Query(() => Float, { nullable: true, name: 'calculateOpTeAccrualAfterReferenceDate' })
    calculateOpTeAccrualAfterReferenceDate(
        @Args('input') input: CalculateOpTeAccrualAfterReferenceDateInput
    ): number | null {
        return this.pensionCalculationsService.calculateOpTeAccrualAfterReferenceDate(input)
    }

    @Query(() => GraphQLJSON, { name: 'calculateRetirementDate' })
    calculateRetirementDate(
        @Args('input') input: CalculateRetirementDateInput
    ): Date {
        return this.pensionCalculationsService.calculateRetirementDate(input)
    }

    @Query(() => Float, { nullable: true, name: 'calculateAccrualPeriodToReferenceDate' })
    calculateAccrualPeriodToReferenceDate(
        @Args('input') input: CalculateAccrualPeriodToReferenceDateInput
    ): number | null {
        return this.pensionCalculationsService.calculateAccrualPeriodToReferenceDate(input)
    }

    @Query(() => Float, { nullable: true, name: 'calculateAccrualPeriodAfterReferenceDate' })
    calculateAccrualPeriodAfterReferenceDate(
        @Args('input') input: CalculateAccrualPeriodAfterReferenceDateInput
    ): number | null {
        return this.pensionCalculationsService.calculateAccrualPeriodAfterReferenceDate(input)
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedPensionInfo' })
    calculateCertifiedPensionInfo(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedPensionInfo({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedIndexationStartOfYear' })
    calculateCertifiedIndexationStartOfYear(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedIndexationStartOfYear({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedPensionCorrectionsStartOfYear' })
    calculateCertifiedPensionCorrectionsStartOfYear(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedPensionCorrectionsStartOfYear({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedPensionCorrectionsEndOfYear' })
    calculateCertifiedPensionCorrectionsEndOfYear(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedPensionCorrectionsEndOfYear({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedPensionCorrections' })
    calculateCertifiedPensionCorrections(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedPensionCorrections({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedEmploymentInfo' })
    calculateCertifiedEmploymentInfo(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedEmploymentInfo({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'calculateCertifiedPensionParameters' })
    calculateCertifiedPensionParameters(
        @Args('year', { type: () => Int }) year: number,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.calculateCertifiedPensionParameters({ year, participantData })
    }

    @Query(() => GraphQLJSON, { name: 'getCalculatedDataByType' })
    getCalculatedDataByType(
        @Args('year', { type: () => Int }) year: number,
        @Args('dataType', { type: () => String }) dataType: string,
        @Args('participantData', { type: () => GraphQLJSON, nullable: true }) participantData?: any
    ) {
        return this.pensionCalculationsService.getCalculatedDataByType({ year, dataType, participantData })
    }
}

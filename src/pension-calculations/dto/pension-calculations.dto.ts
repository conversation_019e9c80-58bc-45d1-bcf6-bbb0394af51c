/**
 * DTOs for Pension Calculations
 */

/**
 * Date structure used in calculations
 */
export interface DateInfo {
    year: number
    month: number
    day: number
}

/**
 * Input for calculating pension base
 */
export interface CalculatePensionBaseDto {
    grossFulltimeAnnualSalary?: number
    offsetAmount?: number
    minimumPensionBase?: number
}

/**
 * Input for calculating OP-TE accrual to reference date
 */
export interface CalculateOpTeAccrualToReferenceDateDto {
    pensionBase?: number
    accrualPercentage?: number
    startDate?: DateInfo
    referenceYear: number
    parttimePercentage?: number
    referenceDate?: DateInfo
}

/**
 * Input for calculating WP-TE accrual to reference date
 */
export interface CalculateWpTeAccrualToReferenceDateDto {
    pensionBase?: number
    accrualPercentage?: number
    partnersPensionPercentage?: number
    startDate?: DateInfo
    referenceYear: number
    parttimePercentage?: number
    referenceDate?: DateInfo
}

/**
 * Input for calculating OP-TE accrual after reference date
 */
export interface CalculateOpTeAccrualAfterReferenceDateDto {
    dateOfBirth?: DateInfo
    retirementAge?: number
    referenceDate: DateInfo | Date
    accrualPercentage?: number
    parttimePercentage?: number
    pensionBase?: number
}

/**
 * Input for calculating retirement date
 */
export interface CalculateRetirementDateDto {
    dateOfBirth: DateInfo
    retirementAge: number
}

/**
 * Input for calculating accrual period to reference date
 */
export interface CalculateAccrualPeriodToReferenceDateDto {
    startDate?: DateInfo
    referenceYear: number
    referenceDate?: DateInfo
}

/**
 * Input for calculating accrual period after reference date
 */
export interface CalculateAccrualPeriodAfterReferenceDateDto {
    dateOfBirth?: DateInfo
    retirementAge?: number
    referenceDate: DateInfo | Date
}

/**
 * Input for calculating certified data
 */
export interface CalculateCertifiedDataDto {
    year: number
    participantData?: any
}

/**
 * Input for getting calculated data by type
 */
export interface GetCalculatedDataByTypeDto {
    year: number
    dataType: string
    participantData?: any
}

/**
 * Certified Pension Info Response
 */
export interface CertifiedPensionInfoResponse {
    __typename: string
    id: string
    code: number
    codeDescription: string
    accruedGrossAnnualOldAgePension: number
    accruedGrossAnnualPartnersPension: number
    accruedGrossAnnualSinglesPension: number
    attainableGrossAnnualOldAgePension: number
    grossAnnualDisabilityPension: number
}

/**
 * Certified Indexation Start of Year Response
 */
export interface CertifiedIndexationStartOfYearResponse {
    __typename: string
    id: string
    accruedGrossAnnualOldAgePension: number
    accruedGrossAnnualPartnersPension: number
    accruedGrossAnnualSinglesPension: number
    extraAccruedGrossAnnualOldAgePension: number
    extraAccruedGrossAnnualPartnersPension: number
    grossAnnualDisabilityPension: number
}

/**
 * Certified Pension Corrections Response
 */
export interface CertifiedPensionCorrectionsResponse {
    __typename: string
    id: string
    year?: string
    accruedGrossAnnualOldAgePension: number
    accruedGrossAnnualPartnersPension: number
    accruedGrossAnnualSinglesPension: number
    attainableGrossAnnualOldAgePension: number
    extraAccruedGrossAnnualOldAgePension: number
    extraAccruedGrossAnnualPartnersPension: number
    grossAnnualDisabilityPension: number
    correction?: number
    pendingChanges: any[]
    requestedChanges: any[]
    approvedChanges: any[]
}

/**
 * Certified Employment Info Response
 */
export interface CertifiedEmploymentInfoResponse {
    __typename: string
    id: string
    department: string
    employeeId: string
    havNum: number
    position: string
    regNum: number
    startDate: string
    status: string
    endDate: string | null
}

/**
 * Certified Pension Parameters Response
 */
export interface CertifiedPensionParametersResponse {
    __typename: string
    id: string
    year: string
    accrualPercentage: number
    annualMultiplier: number
    offsetAmount: number
    minimumPensionBase: number
    partnersPensionPercentage: number
    retirementAge: number
}

/**
 * Certified Voluntary Contributions Response
 */
export interface CertifiedVoluntaryContributionsResponse {
    __typename: string
    id: string
    amount: number
}

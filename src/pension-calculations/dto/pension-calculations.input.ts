import { InputType, Field, Float, Int } from '@nestjs/graphql'

@InputType()
export class DateInfoInput {
    @Field(() => Int)
    year: number

    @Field(() => Int)
    month: number

    @Field(() => Int)
    day: number
}

@InputType()
export class CalculateOpTeAccrualToReferenceDateInput {
    @Field(() => Float, { nullable: true })
    pensionBase?: number

    @Field(() => Float, { nullable: true })
    accrualPercentage?: number

    @Field(() => DateInfoInput, { nullable: true })
    startDate?: DateInfoInput

    @Field(() => Int)
    referenceYear: number

    @Field(() => Float, { nullable: true })
    parttimePercentage?: number

    @Field(() => DateInfoInput, { nullable: true })
    referenceDate?: DateInfoInput
}

@InputType()
export class CalculateWpTeAccrualToReferenceDateInput {
    @Field(() => Float, { nullable: true })
    pensionBase?: number

    @Field(() => Float, { nullable: true })
    accrualPercentage?: number

    @Field(() => Float, { nullable: true })
    partnersPensionPercentage?: number

    @Field(() => DateInfoInput, { nullable: true })
    startDate?: DateInfoInput

    @Field(() => Int)
    referenceYear: number

    @Field(() => Float, { nullable: true })
    parttimePercentage?: number

    @Field(() => DateInfoInput, { nullable: true })
    referenceDate?: DateInfoInput
}

@InputType()
export class CalculateOpTeAccrualAfterReferenceDateInput {
    @Field(() => DateInfoInput, { nullable: true })
    dateOfBirth?: DateInfoInput

    @Field(() => Float, { nullable: true })
    retirementAge?: number

    @Field(() => DateInfoInput)
    referenceDate: DateInfoInput

    @Field(() => Float, { nullable: true })
    accrualPercentage?: number

    @Field(() => Float, { nullable: true })
    parttimePercentage?: number

    @Field(() => Float, { nullable: true })
    pensionBase?: number
}

@InputType()
export class CalculateRetirementDateInput {
    @Field(() => DateInfoInput)
    dateOfBirth: DateInfoInput

    @Field(() => Float)
    retirementAge: number
}

@InputType()
export class CalculateAccrualPeriodToReferenceDateInput {
    @Field(() => DateInfoInput, { nullable: true })
    startDate?: DateInfoInput

    @Field(() => Int)
    referenceYear: number

    @Field(() => DateInfoInput, { nullable: true })
    referenceDate?: DateInfoInput
}

@InputType()
export class CalculateAccrualPeriodAfterReferenceDateInput {
    @Field(() => DateInfoInput, { nullable: true })
    dateOfBirth?: DateInfoInput

    @Field(() => Float, { nullable: true })
    retirementAge?: number

    @Field(() => DateInfoInput)
    referenceDate: DateInfoInput
}

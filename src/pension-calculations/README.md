# Pension Calculations API

This module provides GraphQL queries for various pension calculations that were previously handled in the frontend. All calculations are now centralized in the backend for better maintainability and consistency.

## Available Queries

### Basic Calculations

#### calculatePensionBase
Calculates the pension base from gross fulltime annual salary.
```graphql
query {
  calculatePensionBase(
    grossFulltimeAnnualSalary: 50000
    offsetAmount: 15883
    minimumPensionBase: 0
  )
}
```

### Accrual Calculations

#### calculateOpTeAccrualToReferenceDate
Calculates Old-Age Pension (OP-TE) accrual up to a reference date.
```graphql
query {
  calculateOpTeAccrualToReferenceDate(
    input: {
      pensionBase: 34117
      accrualPercentage: 0.0175
      startDate: { year: 2020, month: 1, day: 1 }
      referenceYear: 2023
      parttimePercentage: 1.0
    }
  )
}
```

#### calculateWpTeAccrualToReferenceDate
Calculates Widows/Partners Pension (WP-TE) accrual up to a reference date.
```graphql
query {
  calculateWpTeAccrualToReferenceDate(
    input: {
      pensionBase: 34117
      accrualPercentage: 0.0175
      partnersPensionPercentage: 0.7
      startDate: { year: 2020, month: 1, day: 1 }
      referenceYear: 2023
      parttimePercentage: 1.0
    }
  )
}
```

#### calculateOpTeAccrualAfterReferenceDate
Calculates future Old-Age Pension accrual from reference date until retirement.
```graphql
query {
  calculateOpTeAccrualAfterReferenceDate(
    input: {
      dateOfBirth: { year: 1985, month: 5, day: 15 }
      retirementAge: 67.5
      referenceDate: { year: 2023, month: 12, day: 31 }
      accrualPercentage: 0.0175
      pensionBase: 34117
    }
  )
}
```

### Date Calculations

#### calculateRetirementDate
Calculates the retirement date based on date of birth and retirement age.
```graphql
query {
  calculateRetirementDate(
    input: {
      dateOfBirth: { year: 1985, month: 5, day: 15 }
      retirementAge: 67.5
    }
  )
}
```

#### calculateAccrualPeriodToReferenceDate
Calculates the accrual period in years up to a reference date.
```graphql
query {
  calculateAccrualPeriodToReferenceDate(
    input: {
      startDate: { year: 2020, month: 1, day: 1 }
      referenceYear: 2023
    }
  )
}
```

#### calculateAccrualPeriodAfterReferenceDate
Calculates the accrual period in years from reference date until retirement.
```graphql
query {
  calculateAccrualPeriodAfterReferenceDate(
    input: {
      dateOfBirth: { year: 1985, month: 5, day: 15 }
      retirementAge: 67.5
      referenceDate: { year: 2023, month: 12, day: 31 }
    }
  )
}
```

### Certified Data Calculations (Fallback/Dummy Data)

These queries provide calculated fallback data when actual certified data is not available.

#### calculateCertifiedPensionInfo
```graphql
query {
  calculateCertifiedPensionInfo(
    year: 2023
    participantData: {}
  )
}
```

#### calculateCertifiedIndexationStartOfYear
```graphql
query {
  calculateCertifiedIndexationStartOfYear(
    year: 2023
    participantData: {}
  )
}
```

#### calculateCertifiedPensionCorrections
```graphql
query {
  calculateCertifiedPensionCorrections(
    year: 2023
    participantData: {}
  )
}
```

#### getCalculatedDataByType
Generic query to get calculated data for any certified data type.
```graphql
query {
  getCalculatedDataByType(
    year: 2023
    dataType: "certifiedPensionInfo"
    participantData: {}
  )
}
```

## Implementation Notes

1. All calculations use the 30/360 day count convention for financial calculations.
2. The dummy certified data calculations currently use placeholder logic and should be replaced with actual business logic.
3. The service is designed to be reusable across different modules and services in the NestJS application.
4. All date inputs use the `DateInfo` structure with year, month, and day fields.

## Using in Other Services

To use the pension calculations in other NestJS services:

```typescript
import { PensionCalculationsService } from '../pension-calculations/pension-calculations.service';

@Injectable()
export class YourService {
  constructor(
    private readonly pensionCalculationsService: PensionCalculationsService
  ) {}

  async someMethod() {
    const pensionBase = this.pensionCalculationsService.calculatePensionBase(
      50000, // gross salary
      15883, // offset
      0      // minimum base
    );
    
    // Use the calculated pension base...
  }
}
```

Remember to import `PensionCalculationsModule` in your module's imports array.

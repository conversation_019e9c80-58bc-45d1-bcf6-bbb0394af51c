import { ObjectType, Field, ID } from '@nestjs/graphql'
import { PersonalInfo } from '../../personal-info/entities/personal-info.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class Address {
    @Field(() => ID)
    id: string

    @Field(() => PersonalInfo)
    personalInfo: PersonalInfo

    @Field({ nullable: true })
    street?: string

    @Field({ nullable: true })
    houseNumber?: string

    @Field({ nullable: true })
    postalCode?: string

    @Field({ nullable: true })
    city?: string

    @Field({ nullable: true })
    state?: string

    @Field({ nullable: true })
    country?: string

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]
}

<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="x-apple-disable-message-reformatting" />
    <title>{{ title }}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* CLIENT-SPECIFIC STYLES */
        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
        table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
        img { -ms-interpolation-mode: bicubic; border: 0; outline: none; text-decoration: none; }

        /* RESET STYLES */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        img { max-width: 100%; height: auto; display: block; }

        /* BASE STYLES */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #2d3748;
            min-height: 100vh;
        }

        table {
            border-collapse: separate;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            width: 100%;
        }

        table td {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-size: 16px;
            vertical-align: top;
        }

        /* CONTAINER */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: transparent;
        }

        .email-wrapper {
            padding: 40px 20px;
        }

        /* HEADER */
        .header {
            text-align: center;
            padding: 30px 0;
        }

        .logo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 32px;
            font-weight: 800;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* MAIN CARD */
        .main-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow:
                    0 20px 25px -5px rgba(0, 0, 0, 0.1),
                    0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            position: relative;
        }

        .main-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .content-wrapper {
            padding: 48px 40px;
        }

        /* SECURITY ICON */
        .security-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .security-icon svg {
            width: 40px;
            height: 40px;
            fill: white;
        }

        /* TYPOGRAPHY */
        .main-title {
            font-size: 28px;
            font-weight: 700;
            color: #1a202c;
            text-align: center;
            margin-bottom: 16px;
            line-height: 1.3;
        }

        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 24px;
        }

        .description {
            color: #4a5568;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 32px;
        }

        /* BUTTON */
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            padding: 16px 40px;
            border-radius: 50px;
            box-shadow:
                    0 10px 15px -3px rgba(102, 126, 234, 0.4),
                    0 4px 6px -2px rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow:
                    0 20px 25px -5px rgba(102, 126, 234, 0.4),
                    0 10px 10px -5px rgba(102, 126, 234, 0.2);
        }

        /* SECURITY NOTE */
        .security-note {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 32px 0;
            border-radius: 8px;
            font-size: 14px;
            color: #4a5568;
        }

        .security-note strong {
            color: #2d3748;
        }

        /* ALTERNATIVE LINK */
        .alt-link-section {
            background: #f8fafc;
            padding: 24px;
            border-radius: 12px;
            margin: 32px 0;
            border: 1px solid #e2e8f0;
        }

        .alt-link-title {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 12px;
        }

        .alt-link {
            font-size: 14px;
            color: #667eea;
            word-break: break-all;
            background: #ffffff;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        /* FOOTER */
        .footer-section {
            border-top: 1px solid #e2e8f0;
            padding-top: 32px;
            margin-top: 40px;
            text-align: center;
        }

        .footer-text {
            color: #718096;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .main-footer {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            padding: 40px;
            text-align: center;
            color: #e2e8f0;
        }

        .footer-logo {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-links {
            margin: 24px 0;
        }

        .footer-links a {
            color: #a0aec0;
            text-decoration: none;
            margin: 0 16px;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #667eea;
        }

        .copyright {
            font-size: 12px;
            color: #718096;
            margin-top: 24px;
        }

        /* PREHEADER */
        .preheader {
            display: none;
            font-size: 1px;
            color: transparent;
            line-height: 1px;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
        }

        /* RESPONSIVE */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 20px 10px;
            }

            .content-wrapper {
                padding: 32px 24px;
            }

            .main-title {
                font-size: 24px;
            }

            .cta-button {
                padding: 14px 32px;
                font-size: 16px;
            }

            .footer-links a {
                display: block;
                margin: 8px 0;
            }

            .main-footer {
                padding: 32px 20px;
            }
        }

        /* DARK MODE SUPPORT */
        @media (prefers-color-scheme: dark) {
            .main-card {
                background: #1a202c;
            }

            .main-title,
            .greeting {
                color: #f7fafc;
            }

            .description {
                color: #e2e8f0;
            }

            .security-note {
                background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
                color: #e2e8f0;
            }

            .alt-link-section {
                background: #2d3748;
                border-color: #4a5568;
            }

            .alt-link {
                background: #4a5568;
                color: #e2e8f0;
                border-color: #718096;
            }
        }
    </style>
</head>
<body>
<div class="preheader">Your password reset link is ready - secure and easy to use.</div>

<table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
        <td>
            <div class="email-container">
                <div class="email-wrapper">

                    <!-- MAIN CONTENT CARD -->
                    <div class="main-card">
                        <div class="content-wrapper">
                            <!-- Security Icon -->
                            <div class="security-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V18H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z"/>
                                </svg>
                            </div>

                            <h1 class="main-title">{{ title }}</h1>
                            <p class="greeting">Hello {{nameUser}},</p>
                            <p class="description">{{description}}</p>

                            <!-- Security Note -->
                            <div class="security-note">
                                <strong>🔒 Security First:</strong> This link will expire in 24 hours for your protection. If you didn't request this reset, you can safely ignore this email.
                            </div>

                            <!-- CTA Button -->
                            <div class="cta-container">
                                <a href="{{resetLink}}" class="cta-button" target="_blank" rel="noopener">
                                    🔑 Reset My Password
                                </a>
                            </div>

                            <!-- Alternative Link -->
                            <div class="alt-link-section">
                                <div class="alt-link-title">Having trouble with the button? Copy this link:</div>
                                <div class="alt-link">{{resetLink}}</div>
                            </div>

                            <!-- Footer Section -->
                            <div class="footer-section">
                                <p class="footer-text">Need help? Our support team is here for you 24/7.</p>
                                <p class="footer-text">{{footer}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MAIN FOOTER -->
            <div class="main-footer">
                <div class="footer-logo">Pension Admin</div>
                <p>Secure • Reliable • Trusted</p>

                <div class="footer-links">
                    <a href="#" target="_blank">Privacy Policy</a>
                    <a href="#" target="_blank">Terms of Service</a>
                    <a href="#" target="_blank">Support Center</a>
                    <a href="#" target="_blank">Contact Us</a>
                </div>

                <div class="copyright">
                    © 2025 Pension Admin. All rights reserved.<br>
                    This email was sent to you as a registered user of our platform.
                </div>
            </div>
        </td>
    </tr>
</table>
</body>
</html>
/**
 * Utility functions for handling date conversions
 */

/**
 * Converts any date string format to ISO string format required by Prisma
 * @param dateStr The date string to convert
 * @returns ISO formatted date string or null if invalid
 */
export function toISODateString(dateStr: string | Date | null): string | null {
    if (!dateStr) return null

    try {
        // If already a Date object
        if (dateStr instanceof Date) {
            return dateStr.toISOString()
        }

        // Try parsing the string date
        const date = new Date(dateStr)

        // Check if valid date
        if (isNaN(date.getTime())) {
            return null
        }

        return date.toISOString()
    } catch (error) {
        console.error('Error converting date to ISO format:', error)
        return null
    }
}

/**
 * Safely converts any date input to a format acceptable by Prisma
 * @param value The date value to convert
 * @returns A Prisma-compatible date string or null
 */
export function toPrismaDate(value: any): string | null {
    if (!value) return null

    // If already a Date object
    if (value instanceof Date) {
        return value.toISOString()
    }

    // If it's a string, try to convert it
    if (typeof value === 'string') {
        return toISODateString(value)
    }

    return null
}

/**
 * Calculates the number of days between two dates using the 30/360 convention
 * Used for financial calculations where all months are considered to have 30 days
 * @param endYear - End date year
 * @param endMonth - End date month (1-12)
 * @param endDay - End date day
 * @param startYear - Start date year
 * @param startMonth - Start date month (1-12)
 * @param startDay - Start date day
 * @returns Number of days using 30/360 convention
 */
export function days360(
    endYear: number,
    endMonth: number,
    endDay: number,
    startYear: number,
    startMonth: number,
    startDay: number
): number {
    // Adjust day values according to 30/360 convention
    let adjustedStartDay = startDay
    let adjustedEndDay = endDay

    // If start day is 31, adjust to 30
    if (adjustedStartDay === 31) {
        adjustedStartDay = 30
    }

    // If end day is 31 and start day is 30 or 31, adjust end day to 30
    if (adjustedEndDay === 31 && startDay >= 30) {
        adjustedEndDay = 30
    }

    // Calculate the difference
    const yearDiff = endYear - startYear
    const monthDiff = endMonth - startMonth
    const dayDiff = adjustedEndDay - adjustedStartDay

    // Return total days (360 days per year, 30 days per month)
    return yearDiff * 360 + monthDiff * 30 + dayDiff
}

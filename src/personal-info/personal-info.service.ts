import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'
import { CreatePersonalInfoInput } from './dto/create-personal-info.input'
import { UpdatePersonalInfoInput } from './dto/update-personal-info.input'

@Injectable()
export class PersonalInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    findAll() {
        return this.prisma.personalInfo.findMany({
            include: {
                participant: true,
                address: true,
                partnerInfo: true,
                children: true,
                certificationRejectReason: true,
            },
        })
    }

    findOne(id: string) {
        const personalInfo = this.prisma.personalInfo.findUnique({
            where: { id },
            include: {
                participant: true,
                address: true,
                partnerInfo: true,
                children: true,
                certificationRejectReason: true,
            },
        })
        if (!personalInfo) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }
        return personalInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const personalInfo = await this.prisma.personalInfo.findUnique({
            where: { id },
        })

        if (!personalInfo) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }

        // Combine existing and new changes, then filter to unique values
        const currentChanges = personalInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedPersonalInfo = await this.prisma.personalInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedPersonalInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedPartner = await this.prisma.personalInfo.update({
            where: { id: entityId },
            data: updateData,
        })

        return updatedPartner
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.personalInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.personalInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async rejectField(
        id: string,
        fieldName: string,
        rejectReason: string,
        userId: string
    ) {
        const personalInfo = await this.prisma.personalInfo.findUnique({
            where: { id },
        })

        if (!personalInfo) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }

        // Add the field to pendingChanges if it's not already there
        const currentPendingChanges = personalInfo.pendingChanges || []
        const updatedPendingChanges = currentPendingChanges.includes(fieldName)
            ? currentPendingChanges
            : [...currentPendingChanges, fieldName]

        // Create the reject reason
        await this.prisma.certificationRejectReason.create({
            data: {
                field: fieldName,
                reason: rejectReason,
                status: 'VALID',
                personalInfo: {
                    connect: { id },
                },
            },
        })

        // Update the PersonalInfo with the pending changes
        const updatedPersonalInfo = await this.prisma.personalInfo.update({
            where: { id },
            data: {
                pendingChanges: updatedPendingChanges,
            },
            include: {
                certificationRejectReason: true,
                address: true,
                partnerInfo: true,
                children: true,
            },
        })

        return updatedPersonalInfo
    }

    remove(id: string) {
        const personalInfo = this.prisma.personalInfo.findUnique({
            where: { id },
        })
        if (!personalInfo) {
            throw new NotFoundException(`PersonalInfo with ID ${id} not found`)
        }
        return this.prisma.personalInfo.delete({
            where: { id },
            include: {
                participant: true,
                address: true,
                partnerInfo: true,
                children: true,
            },
        })
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // Find the personal info record by participant ID
        const personalInfo = await this.prisma.personalInfo.findFirst({
            where: { participantId },
        })

        if (!personalInfo) {
            throw new NotFoundException(
                `PersonalInfo with participant ID ${participantId} not found`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.personalInfo.update({
            where: { id: personalInfo.id },
            data: updateData,
        })
    }
}

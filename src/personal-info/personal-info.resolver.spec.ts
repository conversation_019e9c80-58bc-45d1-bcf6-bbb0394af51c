import { Test, TestingModule } from '@nestjs/testing'
import { PersonalInfoResolver } from './personal-info.resolver'
import { PersonalInfoService } from './personal-info.service'

describe('PersonalInfoResolver', () => {
    let resolver: PersonalInfoResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PersonalInfoResolver, PersonalInfoService],
        }).compile()

        resolver = module.get<PersonalInfoResolver>(PersonalInfoResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

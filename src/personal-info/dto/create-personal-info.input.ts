import { Field, InputType } from '@nestjs/graphql'
import {
    IsArray,
    IsEmail,
    IsNumber,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator'
import { Type } from 'class-transformer'
import {
    CreateNewAddressInput,
    CreateNewChildInput,
    CreateNewPartnerInfoInput,
} from '../../participant/dto/create-participant.input'

@InputType()
export class CreatePersonalInfoInput {
    @Field()
    @IsString()
    firstName: string

    @Field()
    @IsString()
    lastName: string

    @Field()
    @IsString()
    sex: string

    @Field({ nullable: true })
    @IsEmail()
    @IsOptional()
    email?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    phone?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    maritalStatus?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    birthDay?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    birthMonth?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    birthYear?: number

    @Field(() => CreateNewAddressInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateNewAddressInput)
    @IsOptional()
    address?: CreateNewAddressInput

    @Field(() => [CreateNewPartnerInfoInput], { nullable: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateNewPartnerInfoInput)
    @IsOptional()
    partnerInfo?: CreateNewPartnerInfoInput[]

    @Field(() => [CreateNewChildInput], { nullable: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateNewChildInput)
    @IsOptional()
    children?: CreateNewChildInput[]
}

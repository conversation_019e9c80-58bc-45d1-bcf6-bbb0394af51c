import { Args, Mutation, Resolver } from '@nestjs/graphql'
import { AuthService } from './auth.service'
import { ClaimsResponse } from './authTypes/response-types'

@Resolver()
export class AuthResolver {
    constructor(private readonly authService: AuthService) {}

    @Mutation(() => ClaimsResponse, { name: 'setUserClaims' })
    setUserClaims(
        @Args('firebaseUid', { type: () => String })
        firebaseUid: string
    ) {
        return this.authService.setUserClaims({ firebaseUid })
    }

    @Mutation(() => String, { name: 'resetPassword' })
    resetPassword(
        @Args('email', { type: () => String })
        email: string
    ) {
        return this.authService.handleResetPasswordEmail(email)
    }
}

import {
    CanActivate,
    ExecutionContext,
    Injectable,
    Logger,
    UnauthorizedException,
} from '@nestjs/common'
import { Request } from 'express'
import { AuthService } from '../auth.service'
import { GqlExecutionContext } from '@nestjs/graphql'
@Injectable()
export class GraphqlAuthGuard implements CanActivate {
    constructor(private authService: AuthService) {}

    protected user: any
    protected requestVars: any

    private readonly logger = new Logger(GraphqlAuthGuard.name)

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const gqlCtx = context.getArgByIndex(2)
        const request: Request = gqlCtx.req
        const token = this.extractTokenFromAuthHeader(context)
        if (!token) {
            throw new UnauthorizedException()
        }
        try {
            const payload = await this.authService.verifyToken({ token })
            request['user'] = payload.user

            //Set user and req vars to be used in other guards
            this.user = payload.user
            this.requestVars = request['body']['variables']
        } catch (error) {
            console.log(error)
            throw new UnauthorizedException()
        }
        return true
    }

    private extractTokenFromAuthHeader(context: ExecutionContext) {
        const ctx = GqlExecutionContext.create(context)
        const req = ctx.getContext().req
        if (!req) {
            throw new Error('Request object is not available')
        }
        const headers: Headers = req.headers
        if (!headers) {
            throw new Error('Headers are not available')
        }
        const authHeader = headers['authorization']
        const bearer = authHeader?.split(' ')
        return bearer?.[1]
    }
}

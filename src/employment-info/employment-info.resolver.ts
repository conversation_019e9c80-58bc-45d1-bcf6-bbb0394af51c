import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { EmploymentInfoService } from './employment-info.service'
import { EmploymentInfo } from './entities/employment-info.entity'
import { CreateEmploymentInfoInput } from './dto/create-employment-info.input'
import { UpdateEmploymentInfoInput } from './dto/update-employment-info.input'

@Resolver(() => EmploymentInfo)
export class EmploymentInfoResolver {
    constructor(
        private readonly employmentInfoService: EmploymentInfoService
    ) {}

    @Mutation(() => EmploymentInfo)
    async createEmploymentInfo(
        @Args('createEmploymentInfoInput')
        createEmploymentInfoInput: CreateEmploymentInfoInput
    ) {
        return this.employmentInfoService.create(createEmploymentInfoInput)
    }

    @Query(() => [EmploymentInfo], { name: 'employmentInfos' })
    async findAll() {
        return this.employmentInfoService.findAll()
    }

    @Query(() => EmploymentInfo, { name: 'employmentInfo' })
    async findOne(@Args('id') id: string) {
        return this.employmentInfoService.findOne(id)
    }

    @Mutation(() => EmploymentInfo)
    async updateEmploymentInfo(
        @Args('updateEmploymentInfoInput')
        updateEmploymentInfoInput: UpdateEmploymentInfoInput
    ) {
        return this.employmentInfoService.update(
            updateEmploymentInfoInput.id,
            updateEmploymentInfoInput
        )
    }

    @Mutation(() => EmploymentInfo)
    async deleteEmploymentInfo(@Args('id') id: string) {
        return this.employmentInfoService.delete(id)
    }
}

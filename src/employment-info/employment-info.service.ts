import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'
import { CreateEmploymentInfoInput } from './dto/create-employment-info.input'
import { UpdateEmploymentInfoInput } from './dto/update-employment-info.input'
import { EmploymentInfo } from './entities/employment-info.entity'

@Injectable()
export class EmploymentInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    async create(createEmploymentInfoInput: CreateEmploymentInfoInput) {
        const employmentInfo = await this.prisma.employmentInfo.create({
            data: {
                ...createEmploymentInfoInput,
            },
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })

        if (!employmentInfo) {
            throw new NotFoundException('EmploymentInfo not created')
        }

        return employmentInfo
    }

    async findAll() {
        return this.prisma.employmentInfo.findMany({
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })
    }

    async findOne(id: string) {
        const employmentInfo = await this.prisma.employmentInfo.findUnique({
            where: { id },
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })

        if (!employmentInfo) {
            throw new NotFoundException('EmploymentInfo not found')
        }

        return employmentInfo
    }

    async update(
        id: string,
        updateEmploymentInfoInput: UpdateEmploymentInfoInput
    ) {
        const employmentInfo = await this.prisma.employmentInfo.update({
            where: { id: id },
            data: {
                ...updateEmploymentInfoInput,
            },
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })

        if (!employmentInfo) {
            throw new NotFoundException('EmploymentInfo not updated')
        }

        return employmentInfo
    }

    async updateByParticipantId(
        id: string,
        updateEmploymentInfoInput: UpdateEmploymentInfoInput
    ) {
        const employmentInfo = await this.prisma.employmentInfo.update({
            where: { participantId: id },
            data: {
                ...updateEmploymentInfoInput,
            },
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })

        if (!employmentInfo) {
            throw new NotFoundException('EmploymentInfo not updated')
        }

        return employmentInfo
    }

    async delete(id: string) {
        const employmentInfo = await this.prisma.employmentInfo.delete({
            where: { id },
        })

        if (!employmentInfo) {
            throw new NotFoundException('EmploymentInfo not deleted')
        }

        return employmentInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        const updateData = {
            [path]: value,
        }

        const updatedEmploymentInfo = await this.prisma.employmentInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                participant: true,
                salaryEntries: {
                    include: {
                        certificationRejectReason: true,
                    },
                },
                certificationRejectReason: true,
            },
        })

        if (!updatedEmploymentInfo) {
            throw new NotFoundException(
                `EmploymentInfo with ID ${entityId} not updated`
            )
        }

        return updatedEmploymentInfo
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // Find the employment info record by participant ID
        const employmentInfo = await this.prisma.employmentInfo.findFirst({
            where: { participantId },
        })

        if (!employmentInfo) {
            throw new NotFoundException(
                `EmploymentInfo with participant ID ${participantId} not found`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.employmentInfo.update({
            where: { id: employmentInfo.id },
            data: updateData,
        })
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const employmentInfo = await this.prisma.employmentInfo.findUnique({
            where: { id },
        })

        if (!employmentInfo) {
            throw new NotFoundException(
                `EmploymentInfo with ID ${id} not found`
            )
        }

        const currentChanges = employmentInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedEmploymentInfo = await this.prisma.employmentInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedEmploymentInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.employmentInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(
                `EmploymentInfo with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.employmentInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

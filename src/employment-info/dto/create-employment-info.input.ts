import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsNumber, IsDate } from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class CreateEmploymentInfoInput {
    @Field()
    @IsString()
    participantId: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    employeeId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    department?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    position?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    regNum?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    havNum?: number

    @Field({ nullable: true })
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    startDate?: Date

    @Field({ nullable: true })
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    endDate?: Date

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string
}

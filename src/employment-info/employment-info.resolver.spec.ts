import { Test, TestingModule } from '@nestjs/testing'
import { EmploymentInfoResolver } from './employment-info.resolver'
import { EmploymentInfoService } from './employment-info.service'

describe('EmploymentInfoResolver', () => {
    let resolver: EmploymentInfoResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [EmploymentInfoResolver, EmploymentInfoService],
        }).compile()

        resolver = module.get<EmploymentInfoResolver>(EmploymentInfoResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertifiedVoluntaryContributionsInput } from '../certified-data/dto'

@Injectable()
export class CertifiedVoluntaryContributionsService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedContributions =
            await this.prisma.certifiedVoluntaryContributions.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedContributions) {
            throw new NotFoundException(
                `CertifiedVoluntaryContributions with ID ${id} not found`
            )
        }

        return certifiedContributions
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedContributions =
            await this.prisma.certifiedVoluntaryContributions.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedContributions) {
            throw new NotFoundException(
                `CertifiedVoluntaryContributions for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedContributions
    }

    async create(
        createCertifiedVoluntaryContributionsInput: CreateCertifiedVoluntaryContributionsInput,
        certifiedDataId: string
    ) {
        // Extract certificationRejectReason to handle separately
        const { certificationRejectReason, ...contributionsData } =
            createCertifiedVoluntaryContributionsInput

        return this.prisma.certifiedVoluntaryContributions.create({
            data: {
                ...contributionsData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        // Convert to number if the value is numeric
        const processedValue = !isNaN(Number(newValue))
            ? Number(newValue)
            : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo =
            await this.prisma.certifiedVoluntaryContributions.update({
                where: { id: entityId },
                data: updateData,
                include: {
                    certifiedData: true,
                },
            })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedVoluntaryContributions with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedVoluntaryContributions.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedVoluntaryContributions with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo =
            await this.prisma.certifiedVoluntaryContributions.update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate =
            await this.prisma.certifiedVoluntaryContributions.findUnique({
                where: { id },
            })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedVoluntaryContributions with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedVoluntaryContributions.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

import { Module } from '@nestjs/common'
import { CertifiedPensionCorrectionsEndOfYearService } from './certified-pension-corrections-end-of-year.service'
import { CertifiedPensionCorrectionsEndOfYearResolver } from './certified-pension-corrections-end-of-year.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    providers: [
        CertifiedPensionCorrectionsEndOfYearResolver,
        CertifiedPensionCorrectionsEndOfYearService,
    ],
    exports: [CertifiedPensionCorrectionsEndOfYearService],
})
export class CertifiedPensionCorrectionsEndOfYearModule {}

import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionCorrectionsEndOfYearService } from './certified-pension-corrections-end-of-year.service'
import { CertifiedPensionCorrectionsEndOfYear } from '../certified-data/entities/certified-pension-corrections-end-of-year.entity'
import { CreateCertifiedPensionCorrectionsEndOfYearInput } from '../certified-data/dto/create-certified-pension-corrections-end-of-year.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionCorrectionsEndOfYear)
export class CertifiedPensionCorrectionsEndOfYearResolver {
    constructor(
        private readonly certifiedCorrectionsEndService: CertifiedPensionCorrectionsEndOfYearService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrectionsEndOfYear)
    async certifiedPensionCorrectionsEndOfYear(@Args('id') id: string) {
        return this.certifiedCorrectionsEndService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionCorrectionsEndOfYear)
    async certifiedPensionCorrectionsEndOfYearByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedCorrectionsEndService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionCorrectionsEndOfYear)
    async createCertifiedPensionCorrectionsEndOfYear(
        @Args('createCertifiedPensionCorrectionsEndOfYearInput')
        createCertifiedPensionCorrectionsEndOfYearInput: CreateCertifiedPensionCorrectionsEndOfYearInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedCorrectionsEndService.create(
            createCertifiedPensionCorrectionsEndOfYearInput,
            certifiedDataId
        )
    }
}

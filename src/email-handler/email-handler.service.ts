import { Injectable, Logger } from '@nestjs/common'
import { MailerService } from '@nestjs-modules/mailer'
import { NewUserInput, ResetPasswordInput } from './dto/welcome-email.input'

@Injectable()
export class EmailHandlerService {
    constructor(private readonly mailerService: MailerService) {}

    async sendResetPasswordEmail(data: ResetPasswordInput) {
        const { email, resetLink } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '<EMAIL>',
                subject: 'Please reset your password',
                text: 'Reset password!',
                template: 'resetPassword',
                context: {
                    title: 'Reset Password',
                    description:
                        'Do you need to reset your password? No problem! Simply click the button below.✔ If you did not request a password reset, please ignore this email.✔',
                    nameUser: email,
                    name: email,
                    resetLink: resetLink,
                    footer: 'Best regards,\n' + 'Your Pension Admin Team',
                },
            })
            Logger.log('[MailService] Password Reset: Email send successfully!')
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] Password Reset: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }

    async sendNewUserEmail(data: NewUserInput) {
        const { email, resetLink, name } = data
        let response = ''
        try {
            await this.mailerService.sendMail({
                to: email,
                from: '<EMAIL>',
                subject: 'Welcome to Pension Admin!',
                text: 'Hello!',
                template: 'resetPassword',
                context: {
                    title: 'Welcome to Pension Admin! ',
                    description:
                        'welcome to Pension Admin! To set your password and confirm your email address, please click on the following link:',
                    nameUser: email,
                    name: name,
                    resetLink: resetLink,
                    footer: 'Best regards,\n' + 'Your Pension Admin Team',
                },
            })
            Logger.log(
                '[MailService] New Company User : Email send successfully!'
            )
            response = 'Email sent successfully'
        } catch (error) {
            Logger.error(
                '[MailService] New Company User: Send Mail failed!',
                error
            )
            response = 'Email send failed'
        }
        return response
    }
}

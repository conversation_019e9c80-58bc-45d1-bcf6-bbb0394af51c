import { Module } from '@nestjs/common'
import { ChangeProposalService } from './change-proposal.service'
import { ChangeProposalResolver } from './change-proposal.resolver'
import { PersonalInfoModule } from '../personal-info/personal-info.module'
import { PartnerInfoModule } from '../partner-info/partner-info.module'
import { ChildModule } from '../child/child.module'
import { AddressModule } from '../address/address.module'
import { PensionParametersModule } from '../pension-parameters/pension-parameters.module'
import { SalaryEntryModule } from '../salary-entry/salary-entry.module'
import { PensionInfoModule } from '../pension-info/pension-info.module'
import { CertifiedPersonalInfoModule } from '../certified-personal-info/certified-personal-info.module'
import { CertifiedPensionInfoModule } from '../certified-pension-info/certified-pension-info.module'
import { CertifiedPensionParametersModule } from '../certified-pension-parameters/certified-pension-parameters.module'
import { CertifiedEmploymentInfoModule } from '../certified-employment-info/certified-employment-info.module'
import { CertifiedIndexationStartOfYearModule } from '../certified-indexation-start-of-year/certified-indexation-start-of-year.module'
import { CertifiedPensionCorrectionsModule } from '../certified-pension-corrections/certified-pension-corrections.module'
import { CertifiedVoluntaryContributionsModule } from '../certified-voluntary-contributions/certified-voluntary-contributions.module'
import { EmploymentInfoModule } from '../employment-info/employment-info.module'
import { CertificationRejectReasonModule } from '../certification-reject-reason/certification-reject-reason.module'
import { CertifiedChildModule } from '../certified-child/certified-child.module'
import { CertifiedPartnerInfoModule } from '../certified-partner-info/certified-partner-info.module'
import { CertifiedAddressModule } from '../certified-address/certified-address.module'
import { NotificationsModule } from '../notifications/notifications.module'
import { UserModule } from '../user/user.module'

@Module({
    imports: [
        PersonalInfoModule,
        PartnerInfoModule,
        AddressModule,
        ChildModule,
        PensionParametersModule,
        SalaryEntryModule,
        EmploymentInfoModule,
        PensionInfoModule,
        CertifiedPersonalInfoModule,
        CertifiedPensionInfoModule,
        CertifiedPensionParametersModule,
        CertifiedEmploymentInfoModule,
        CertifiedIndexationStartOfYearModule,
        CertifiedPensionCorrectionsModule,
        CertifiedVoluntaryContributionsModule,
        CertificationRejectReasonModule,
        CertifiedPartnerInfoModule,
        CertifiedAddressModule,
        CertifiedChildModule,
        NotificationsModule,
        UserModule,
    ],
    exports: [ChangeProposalService],
    providers: [ChangeProposalResolver, ChangeProposalService],
})
export class ChangeProposalModule {}

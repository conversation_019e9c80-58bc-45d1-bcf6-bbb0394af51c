import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BaseService } from '../common/base.service'
import { CreatePartnerInfoInput } from './dto/create-partner-info.input'
import { UpdatePartnerInfoInput } from './dto/update-partner-info.input'

@Injectable()
export class PartnerInfoService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super()
    }

    private async checkDateOverlaps(
        personalInfoId: string,
        startDate: Date | null,
        endDate: Date | null,
        excludeId?: string
    ) {
        if (!startDate) return

        // Get all existing partners for this personal info
        const existingPartners = await this.prisma.partnerInfo.findMany({
            where: {
                personalInfoId,
                id: excludeId ? { not: excludeId } : undefined,
            },
        })

        for (const partner of existingPartners) {
            if (!partner.startDate) continue

            const existingStart = new Date(partner.startDate)
            const existingEnd = partner.endDate
                ? new Date(partner.endDate)
                : null
            const newStart = new Date(startDate)
            const newEnd = endDate ? new Date(endDate) : null

            // Check for overlaps
            const hasOverlap = this.datesOverlap(
                newStart,
                newEnd,
                existingStart,
                existingEnd
            )

            if (hasOverlap) {
                const partnerName =
                    partner.firstName && partner.lastName
                        ? `${partner.firstName} ${partner.lastName}`
                        : 'Unnamed partner'
                throw new BadRequestException(
                    `Partner dates overlap with existing partner: ${partnerName} (${existingStart.toDateString()}${
                        existingEnd
                            ? ' - ' + existingEnd.toDateString()
                            : ' - ongoing'
                    })`
                )
            }
        }
    }

    private datesOverlap(
        start1: Date,
        end1: Date | null,
        start2: Date,
        end2: Date | null
    ): boolean {
        // If either period has no end date, treat it as ongoing (infinite end)
        const effectiveEnd1 = end1 || new Date('9999-12-31')
        const effectiveEnd2 = end2 || new Date('9999-12-31')

        // Two periods overlap if:
        // start1 <= effectiveEnd2 AND start2 <= effectiveEnd1
        return start1 <= effectiveEnd2 && start2 <= effectiveEnd1
    }

    async create(createPartnerInfoInput: CreatePartnerInfoInput) {
        // Check for date overlaps before creating
        await this.checkDateOverlaps(
            createPartnerInfoInput.personalInfoId,
            createPartnerInfoInput.startDate || null,
            createPartnerInfoInput.endDate || null
        )

        return this.prisma.partnerInfo.create({
            data: createPartnerInfoInput,
            include: {
                personalInfo: true,
            },
        })
    }

    findAll() {
        return this.prisma.partnerInfo.findMany({
            include: {
                personalInfo: true,
            },
        })
    }

    findOne(id: string) {
        const partnerInfo = this.prisma.partnerInfo.findUnique({
            where: { id },
            include: {
                personalInfo: true,
            },
        })
        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }
        return partnerInfo
    }

    async update(updatePartnerInfoInput: UpdatePartnerInfoInput) {
        const partnerInfo = await this.prisma.partnerInfo.findUnique({
            where: { id: updatePartnerInfoInput.id },
        })
        if (!partnerInfo) {
            throw new NotFoundException(
                `PartnerInfo with ID ${updatePartnerInfoInput.id} not found`
            )
        }

        // Check for date overlaps before updating (if dates are being changed)
        if (
            updatePartnerInfoInput.startDate !== undefined ||
            updatePartnerInfoInput.endDate !== undefined
        ) {
            const newStartDate =
                updatePartnerInfoInput.startDate !== undefined
                    ? updatePartnerInfoInput.startDate
                    : partnerInfo.startDate
            const newEndDate =
                updatePartnerInfoInput.endDate !== undefined
                    ? updatePartnerInfoInput.endDate
                    : partnerInfo.endDate

            await this.checkDateOverlaps(
                partnerInfo.personalInfoId,
                newStartDate,
                newEndDate,
                updatePartnerInfoInput.id
            )
        }

        return this.prisma.partnerInfo.update({
            where: { id: updatePartnerInfoInput.id },
            data: updatePartnerInfoInput,
            include: {
                personalInfo: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const value = this.handleDateFields(path, newValue)

        // Get current partner info for validation
        const currentPartner = await this.prisma.partnerInfo.findUnique({
            where: { id: entityId },
        })

        if (!currentPartner) {
            throw new NotFoundException(
                `PartnerInfo with ID ${entityId} not found`
            )
        }

        // Check for date overlaps if updating date fields
        if (path === 'startDate' || path === 'endDate') {
            const newStartDate =
                path === 'startDate' ? value : currentPartner.startDate
            const newEndDate =
                path === 'endDate' ? value : currentPartner.endDate

            await this.checkDateOverlaps(
                currentPartner.personalInfoId,
                newStartDate,
                newEndDate,
                entityId
            )
        }

        const updateData = {
            [path]: value,
        }

        const updatedPartner = await this.prisma.partnerInfo.update({
            where: { id: entityId },
            data: updateData,
            include: {
                personalInfo: true,
            },
        })

        return updatedPartner
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.partnerInfo.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.partnerInfo.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const partnerInfo = await this.prisma.partnerInfo.findUnique({
            where: { id },
        })

        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }

        const currentChanges = partnerInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedPartnerInfo = await this.prisma.partnerInfo.update({
            where: { id },
            data: {
                pendingChanges: uniqueChanges,
            },
        })

        return updatedPartnerInfo
    }

    async updateFieldByParticipantId(data: {
        participantId: string
        path: string
        newValue: any
    }) {
        const { participantId, path, newValue } = data

        // For partner info, we need to find through personalInfo
        const personalInfo = await this.prisma.personalInfo.findFirst({
            where: { participantId },
            include: { partnerInfo: true },
        })

        if (!personalInfo || !personalInfo.partnerInfo?.length) {
            throw new NotFoundException(
                `No partner info found for participant ID ${participantId}`
            )
        }

        // For simplicity, update the current partner (isCurrent=true) or the first one if none is marked as current
        let partnerToUpdate = personalInfo.partnerInfo.find(
            (partner) => partner.isCurrent === true
        )
        if (!partnerToUpdate && personalInfo.partnerInfo.length > 0) {
            partnerToUpdate = personalInfo.partnerInfo[0]
        }

        if (!partnerToUpdate) {
            throw new NotFoundException(
                `No suitable partner found for participant ID ${participantId}`
            )
        }

        // Update the specific field
        const updateData = {
            [path]: newValue,
        }

        return this.prisma.partnerInfo.update({
            where: { id: partnerToUpdate.id },
            data: updateData,
        })
    }

    remove(id: string) {
        const partnerInfo = this.prisma.partnerInfo.findUnique({
            where: { id },
        })
        if (!partnerInfo) {
            throw new NotFoundException(`PartnerInfo with ID ${id} not found`)
        }
        return this.prisma.partnerInfo.delete({
            where: { id },
            include: {
                personalInfo: true,
            },
        })
    }
}

import { Test, TestingModule } from '@nestjs/testing'
import { PartnerInfoResolver } from './partner-info.resolver'
import { PartnerInfoService } from './partner-info.service'

describe('PartnerInfoResolver', () => {
    let resolver: PartnerInfoResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PartnerInfoResolver, PartnerInfoService],
        }).compile()

        resolver = module.get<PartnerInfoResolver>(PartnerInfoResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

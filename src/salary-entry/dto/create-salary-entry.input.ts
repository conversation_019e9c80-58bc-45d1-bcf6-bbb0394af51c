import { InputType, Field } from '@nestjs/graphql'
import {
    IsUUID,
    IsInt,
    IsNumber,
    IsPositive,
    Min,
    Max,
    IsOptional,
    IsString,
} from 'class-validator'

@InputType()
export class CreateSalaryEntryInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    employmentInfoId: string

    @Field()
    @IsInt()
    @Min(1900)
    @Max(2100)
    year: number

    @Field()
    @IsNumber()
    @IsPositive()
    amount: number

    @Field()
    @IsNumber()
    @Min(0)
    @Max(100)
    partTimePercentage: number
}

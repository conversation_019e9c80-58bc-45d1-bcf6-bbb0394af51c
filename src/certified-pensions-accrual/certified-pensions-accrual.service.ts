import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCertifiedPensionsAccrualInput } from '../certified-data/dto'

@Injectable()
export class CertifiedPensionsAccrualService {
    constructor(private readonly prisma: PrismaService) {}

    async findOne(id: string) {
        const certifiedAccrual =
            await this.prisma.certifiedPensionsAccrual.findUnique({
                where: { id },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedAccrual) {
            throw new NotFoundException(
                `CertifiedPensionsAccrual with ID ${id} not found`
            )
        }

        return certifiedAccrual
    }

    async findByCertifiedDataId(certifiedDataId: string) {
        const certifiedAccrual =
            await this.prisma.certifiedPensionsAccrual.findUnique({
                where: { certifiedDataId },
                include: {
                    certifiedData: true,
                },
            })

        if (!certifiedAccrual) {
            throw new NotFoundException(
                `CertifiedPensionsAccrual for certifiedDataId ${certifiedDataId} not found`
            )
        }

        return certifiedAccrual
    }

    async create(
        createCertifiedPensionsAccrualInput: CreateCertifiedPensionsAccrualInput,
        certifiedDataId: string
    ) {
        const { certificationRejectReason, ...accrualData } =
            createCertifiedPensionsAccrualInput as any

        return this.prisma.certifiedPensionsAccrual.create({
            data: {
                ...accrualData,
                certifiedData: {
                    connect: { id: certifiedDataId },
                },
            },
            include: {
                certifiedData: true,
            },
        })
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const processedValue = !isNaN(Number(newValue))
            ? Number(newValue)
            : newValue

        const updateData = {
            [path]: processedValue,
        }

        const updatedInfo =
            await this.prisma.certifiedPensionsAccrual.update({
                where: { id: entityId },
                data: updateData,
                include: {
                    certifiedData: true,
                },
            })

        if (!updatedInfo) {
            throw new NotFoundException(
                `CertifiedPensionsAccrual with ID ${entityId} not updated`
            )
        }

        return updatedInfo
    }

    async updatePendingChanges(id: string, changes: string[]) {
        const certifiedInfo =
            await this.prisma.certifiedPensionsAccrual.findUnique({
                where: { id },
            })

        if (!certifiedInfo) {
            throw new NotFoundException(
                `CertifiedPensionsAccrual with ID ${id} not found`
            )
        }

        const currentChanges = certifiedInfo.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        const updatedInfo =
            await this.prisma.certifiedPensionsAccrual.update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })

        return updatedInfo
    }

    async clearPendingChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate =
            await this.prisma.certifiedPensionsAccrual.findUnique({
                where: { id },
            })

        if (!toUpdate) {
            throw new NotFoundException(
                `CertifiedPensionsAccrual with ID ${id} not found`
            )
        }

        const currentChanges = toUpdate.pendingChanges || []
        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.certifiedPensionsAccrual.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
}

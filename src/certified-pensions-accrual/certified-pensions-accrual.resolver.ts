import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CertifiedPensionsAccrualService } from './certified-pensions-accrual.service'
import { CertifiedPensionsAccrual } from '../certified-data/entities/certified-pensions-accrual.entity'
import { CreateCertifiedPensionsAccrualInput } from '../certified-data/dto/create-certified-pensions-accrual.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => CertifiedPensionsAccrual)
export class CertifiedPensionsAccrualResolver {
    constructor(
        private readonly certifiedAccrualService: CertifiedPensionsAccrualService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionsAccrual)
    async certifiedPensionsAccrual(@Args('id') id: string) {
        return this.certifiedAccrualService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedPensionsAccrual)
    async certifiedPensionsAccrualByCertifiedDataId(
        @Args('certifiedDataId') certifiedDataId: string
    ) {
        return this.certifiedAccrualService.findByCertifiedDataId(
            certifiedDataId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedPensionsAccrual)
    async createCertifiedPensionsAccrual(
        @Args('createCertifiedPensionsAccrualInput')
        createCertifiedPensionsAccrualInput: CreateCertifiedPensionsAccrualInput,
        @Args('certifiedDataId') certifiedDataId: string,
        @Context() context: any
    ) {
        return this.certifiedAccrualService.create(
            createCertifiedPensionsAccrualInput,
            certifiedDataId
        )
    }
}

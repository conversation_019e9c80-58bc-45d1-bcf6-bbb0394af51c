import { Module } from '@nestjs/common'
import { NotificationResolver } from './notifications.resolver'
import { NotificationService } from './notifications.service'
import { PubSubModule } from '../pub-sub/pub-sub.module'
import { AuthModule } from '../auth/auth.module'
import { PusherModule } from '../pusher/pusher.module'

@Module({
    imports: [PubSubModule, AuthModule, PusherModule],
    exports: [NotificationService],
    providers: [NotificationResolver, NotificationService],
})
export class NotificationsModule {}

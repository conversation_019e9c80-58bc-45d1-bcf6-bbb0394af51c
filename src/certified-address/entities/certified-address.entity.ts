import { ObjectType, Field, ID } from '@nestjs/graphql'
import { CertifiedData } from '../../certified-data/entities/certified-data.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedAddress {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field({ nullable: true })
    street?: string

    @Field({ nullable: true })
    houseNumber?: string

    @Field({ nullable: true })
    postalCode?: string

    @Field({ nullable: true })
    city?: string

    @Field({ nullable: true })
    state?: string

    @Field({ nullable: true })
    country?: string

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}

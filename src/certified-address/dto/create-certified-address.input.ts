import { InputType, Field } from '@nestjs/graphql'
import { IsOptional, IsString, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedAddressInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    street?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    houseNumber?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    postalCode?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    city?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    state?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    country?: string

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}

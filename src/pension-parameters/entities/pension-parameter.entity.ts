import {
    ObjectType,
    Field,
    Float,
    Int,
    GraphQLISODateTime,
} from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'

@ObjectType()
export class PensionParameters {
    @Field(() => String)
    id: string

    @Field(() => Float)
    accrualPercentage: number

    @Field(() => Float)
    annualMultiplier: number

    @Field(() => Float)
    offsetAmount: number

    @Field(() => Float)
    partnersPensionPercentage: number

    @Field(() => Int)
    retirementAge: number

    @Field(() => Float)
    voluntaryContributionInterestRate: number

    @Field(() => Float)
    minimumPensionBase: number

    @Field(() => Float)
    indexationPercentage: number

    @Field()
    year: string

    @Field(() => GraphQLISODateTime)
    effectiveDate: Date

    @Field(() => GraphQLISODateTime)
    updatedAt: Date

    @Field(() => User)
    updatedBy: User

    @Field(() => GraphQLISODateTime)
    createdAt: Date

    @Field()
    userId: string

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => String, { nullable: true })
    status?: string

    @Field(() => String, { nullable: true })
    rejectReason?: string
}

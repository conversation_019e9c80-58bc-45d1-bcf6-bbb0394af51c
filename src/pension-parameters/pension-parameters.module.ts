import { Module, forwardRef } from '@nestjs/common'
import { PensionParametersService } from './pension-parameters.service'
import { PensionParametersResolver } from './pension-parameters.resolver'
import { ChangeProposalModule } from '../change-proposal/change-proposal.module'
import { NotificationsModule } from '../notifications/notifications.module'
import { UserModule } from '../user/user.module'

@Module({
    imports: [
        forwardRef(() => ChangeProposalModule),
        NotificationsModule,
        UserModule,
    ],
    exports: [PensionParametersService],
    providers: [PensionParametersResolver, PensionParametersService],
})
export class PensionParametersModule {}

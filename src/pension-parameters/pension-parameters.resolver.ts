import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { PensionParametersService } from './pension-parameters.service'
import { PensionParameters } from './entities/pension-parameter.entity'
import { CreatePensionParametersInput } from './dto/create-pension-parameter.input'
import { UpdatePensionParametersInput } from './dto/update-pension-parameter.input'

@Resolver(() => PensionParameters)
export class PensionParametersResolver {
    constructor(
        private readonly pensionParametersService: PensionParametersService
    ) {}

    @Mutation(() => PensionParameters)
    createPensionParameters(
        @Args('createPensionParametersInput')
        createPensionParametersInput: CreatePensionParametersInput
    ) {
        return this.pensionParametersService.create(
            createPensionParametersInput
        )
    }

    @Query(() => [PensionParameters], { name: 'getAllPensionParameters' })
    findAll() {
        return this.pensionParametersService.findAll()
    }

    @Query(() => PensionParameters, { name: 'getPensionParamById' })
    findOne(@Args('id', { type: () => ID }) id: string) {
        return this.pensionParametersService.findOne(id)
    }

    @Mutation(() => PensionParameters)
    updatePensionParameters(
        @Args('updatePensionParametersInput')
        updatePensionParametersInput: UpdatePensionParametersInput
    ) {
        return this.pensionParametersService.update(
            updatePensionParametersInput
        )
    }

    @Mutation(() => PensionParameters)
    removePensionParameters(@Args('id', { type: () => ID }) id: string) {
        return this.pensionParametersService.remove(id)
    }

    @Mutation(() => PensionParameters)
    approvePensionParameters(@Args('id', { type: () => ID }) id: string) {
        return this.pensionParametersService.approve(id)
    }

    @Mutation(() => PensionParameters)
    rejectPensionParameters(
        @Args('id', { type: () => ID }) id: string,
        @Args('rejectReason') rejectReason: string,
        @Args('userId') userId: string,
        @Args('rejectedFields', { type: () => [String] })
        rejectedFields: string[]
    ) {
        return this.pensionParametersService.reject(
            id,
            rejectReason,
            userId,
            rejectedFields
        )
    }

    @Query(() => [PensionParameters], { name: 'getPendingPensionParameters' })
    findPending() {
        return this.pensionParametersService.findPending()
    }
}

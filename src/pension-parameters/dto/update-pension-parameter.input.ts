import { InputType, Field, PartialType } from '@nestjs/graphql'
import { IsString, IsOptional } from 'class-validator'
import { CreatePensionParametersInput } from './create-pension-parameter.input'

@InputType()
export class UpdatePensionParametersInput extends PartialType(
    CreatePensionParametersInput
) {
    @IsString()
    @Field()
    id: string

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    status?: string
}

import { Test, TestingModule } from '@nestjs/testing'
import { PensionParametersResolver } from './pension-parameters.resolver'
import { PensionParametersService } from './pension-parameters.service'

describe('PensionParametersResolver', () => {
    let resolver: PensionParametersResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PensionParametersResolver, PensionParametersService],
        }).compile()

        resolver = module.get<PensionParametersResolver>(
            PensionParametersResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

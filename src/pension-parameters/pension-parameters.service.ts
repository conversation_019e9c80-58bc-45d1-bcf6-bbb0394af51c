import {
    Injectable,
    NotFoundException,
    Inject,
    forwardRef,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePensionParametersInput } from './dto/create-pension-parameter.input'
import { UpdatePensionParametersInput } from './dto/update-pension-parameter.input'
import { ChangeProposalService } from '../change-proposal/change-proposal.service'
import { ChangeType } from '@prisma/client'
import { NotificationService } from '../notifications/notifications.service'
import { UserService } from '../user/user.service'

@Injectable()
export class PensionParametersService {
    constructor(
        private readonly prisma: PrismaService,
        @Inject(forwardRef(() => ChangeProposalService))
        private readonly changeProposalService: ChangeProposalService,
        private readonly notificationService: NotificationService,
        private readonly userService: UserService
    ) {}

    async create(createPensionParametersInput: CreatePensionParametersInput) {
        //check if a parameter with the same year already exists
        const existingParameter = await this.prisma.pensionParameters.findFirst(
            {
                where: { year: createPensionParametersInput?.year },
            }
        )
        if (existingParameter?.year === createPensionParametersInput?.year) {
            throw new NotFoundException(
                `PensionParameters for year ${createPensionParametersInput.year} already exists`
            )
        }

        const pensionParameters = await this.prisma.pensionParameters.create({
            data: {
                ...createPensionParametersInput,
                effectiveDate: new Date(
                    createPensionParametersInput.effectiveDate
                ),
            },
            include: {
                updatedBy: true,
            },
        })

        // Send notifications to admins and reviewers
        await this.notifyPensionParametersCreated(
            pensionParameters,
            createPensionParametersInput.userId
        )

        return pensionParameters
    }

    findAll() {
        return this.prisma.pensionParameters.findMany({
            include: {
                updatedBy: true,
            },
        })
    }

    findOne(id: string) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id },
            include: {
                updatedBy: true,
            },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }
        return pensionParameters
    }

    update(updatePensionParametersInput: UpdatePensionParametersInput) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id: updatePensionParametersInput.id },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${updatePensionParametersInput.id} not found`
            )
        }

        return this.prisma.pensionParameters.update({
            where: { id: updatePensionParametersInput.id },
            data: updatePensionParametersInput,
            include: {
                updatedBy: true,
            },
        })
    }

    async updatePendingChanges(
        id: string,
        changes: string[],
        changeProposalUpdate = true
    ) {
        const pensionParams = await this.prisma.pensionParameters.findUnique({
            where: { id },
        })

        if (!pensionParams) {
            throw new NotFoundException(`PensionParams with ID ${id} not found`)
        }

        const currentChanges = pensionParams.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...changes])]

        // Prepare update data
        const updateData: any = {
            pendingChanges: uniqueChanges,
        }

        // If status is rejected, set it back to pending since a change proposal has been submitted
        if (pensionParams.status === 'rejected' && changeProposalUpdate) {
            updateData.status = 'pending'
            updateData.rejectReason = null // Clear the reject reason
        }

        const pensionParamUpdatedInfo =
            await this.prisma.pensionParameters.update({
                where: { id },
                data: updateData,
            })

        return pensionParamUpdatedInfo
    }

    async changeUpdate(changes: any) {
        const { entityId, newValue, path } = changes

        const updateData = {
            [path]: Number(newValue),
        }

        const updatedPartner = await this.prisma.pensionParameters.update({
            where: { id: entityId },
            data: updateData,
        })
    }

    async clearPendingParameterChanges(id: string, paths: string | string[]) {
        const pathsArray = Array.isArray(paths) ? paths : [paths]

        const toUpdate = await this.prisma.pensionParameters.findUnique({
            where: { id },
        })

        if (!toUpdate) {
            throw new NotFoundException(`PensionParam with ID ${id} not found`)
        }

        const currentChanges = toUpdate.pendingChanges || []

        const updatedChanges = currentChanges.filter(
            (path) => !pathsArray.includes(path)
        )

        return this.prisma.pensionParameters.update({
            where: { id },
            data: { pendingChanges: updatedChanges },
        })
    }
    remove(id: string) {
        const pensionParameters = this.prisma.pensionParameters.findUnique({
            where: { id },
        })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }
        return this.prisma.pensionParameters.delete({
            where: { id },
            include: {
                updatedBy: true,
            },
        })
    }

    async findPending() {
        return this.prisma.pensionParameters.findMany({
            where: { status: 'pending' },
            include: {
                updatedBy: true,
            },
        })
    }

    async approve(id: string) {
        const pensionParameters =
            await this.prisma.pensionParameters.findUnique({
                where: { id },
                include: {
                    updatedBy: true,
                },
            })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }

        const updatedParams = await this.prisma.pensionParameters.update({
            where: { id },
            data: {
                status: 'active',
                rejectReason: null,
            },
            include: {
                updatedBy: true,
            },
        })

        await this.updatePendingChanges(id, [], false)

        // Send notifications to admins and editors
        await this.notifyPensionParametersApproved(updatedParams)

        return updatedParams
    }

    async reject(
        id: string,
        rejectReason: string,
        userId: string,
        rejectedFields: string[]
    ) {
        const pensionParameters =
            await this.prisma.pensionParameters.findUnique({
                where: { id },
                include: {
                    updatedBy: true,
                },
            })
        if (!pensionParameters) {
            throw new NotFoundException(
                `PensionParameters with ID ${id} not found`
            )
        }

        const updatedParams = await this.prisma.pensionParameters.update({
            where: { id },
            data: {
                status: 'rejected',
                rejectReason: rejectReason,
            },
            include: {
                updatedBy: true,
            },
        })

        // Update pendingChanges with the selected rejected fields
        await this.updatePendingChanges(id, rejectedFields, false)

        // Send notifications to admins and reviewers
        await this.notifyPensionParametersRejected(
            updatedParams,
            rejectReason,
            rejectedFields
        )

        return updatedParams
    }

    private async notifyPensionParametersCreated(
        pensionParameters: any,
        createdById: string
    ): Promise<void> {
        try {
            const users = await this.userService.findAll()
            const adminsAndReviewers = users.filter(
                (user) =>
                    user.role &&
                    (user.role.name.toLowerCase() === 'admin' ||
                        user.role.name.toLowerCase() === 'reviewer')
            )

            const notificationPromises = adminsAndReviewers.map((user) =>
                this.notificationService.create(
                    {
                        message: `New pension parameters for year ${pensionParameters.year} have been created and require review.`,
                        recipientId: user.id,
                        type: 'PENSION_PARAMETERS_CREATED',
                        entityId: pensionParameters.id,
                        entityType: 'PENSION_PARAMETERS',
                    },
                    createdById
                )
            )

            await Promise.all(notificationPromises)
        } catch (error) {
            console.error('Failed to notify admins and reviewers:', error)
        }
    }

    private async notifyPensionParametersApproved(
        pensionParameters: any
    ): Promise<void> {
        try {
            const users = await this.userService.findAll()
            const adminsAndReviewers = users.filter(
                (user) =>
                    user.role &&
                    (user.role.name.toLowerCase() === 'admin' ||
                        user.role.name.toLowerCase() === 'editor')
            )

            const notificationPromises = adminsAndReviewers.map((user) =>
                this.notificationService.create(
                    {
                        message: `Pension parameters for year ${pensionParameters.year} have been approved.`,
                        recipientId: user.id,
                        type: 'PENSION_PARAMETERS_APPROVED',
                        entityId: pensionParameters.id,
                        entityType: 'PENSION_PARAMETERS',
                    },
                    pensionParameters.userId
                )
            )

            await Promise.all(notificationPromises)
        } catch (error) {
            console.error('Failed to notify admins and editors:', error)
        }
    }

    private async notifyPensionParametersRejected(
        pensionParameters: any,
        rejectReason: string,
        rejectedFields: string[]
    ): Promise<void> {
        try {
            const users = await this.userService.findAll()
            const adminsAndReviewers = users.filter(
                (user) =>
                    user.role &&
                    (user.role.name.toLowerCase() === 'admin' ||
                        user.role.name.toLowerCase() === 'editor')
            )

            const fieldsText =
                rejectedFields.length > 0
                    ? ` Fields requiring changes: ${rejectedFields.join(', ')}.`
                    : ''

            const notificationPromises = adminsAndReviewers.map((user) =>
                this.notificationService.create(
                    {
                        message: `Pension parameters for year ${pensionParameters.year} have been rejected. Reason: ${rejectReason}.${fieldsText}`,
                        recipientId: user.id,
                        type: 'PENSION_PARAMETERS_REJECTED',
                        entityId: pensionParameters.id,
                        entityType: 'PENSION_PARAMETERS',
                    },
                    pensionParameters.userId
                )
            )

            await Promise.all(notificationPromises)
        } catch (error) {
            console.error('Failed to notify admins and reviewers:', error)
        }
    }
}

import { Test, TestingModule } from '@nestjs/testing'
import { CertifiedPartnerInfoResolver } from './certified-partner-info.resolver'
import { CertifiedPartnerInfoService } from './certified-partner-info.service'

describe('CertifiedPartnerInfoResolver', () => {
    let resolver: CertifiedPartnerInfoResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CertifiedPartnerInfoResolver,
                CertifiedPartnerInfoService,
            ],
        }).compile()

        resolver = module.get<CertifiedPartnerInfoResolver>(
            CertifiedPartnerInfoResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})

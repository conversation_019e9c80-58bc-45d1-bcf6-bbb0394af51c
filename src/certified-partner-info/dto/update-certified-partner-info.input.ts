import { InputType, Field, ID } from '@nestjs/graphql'
import { CreateCertifiedPartnerInfoInput } from './create-certified-partner-info.input'
import { IsString } from 'class-validator'
import { PartialType } from '@nestjs/mapped-types'

@InputType()
export class UpdateCertifiedPartnerInfoInput extends PartialType(
    CreateCertifiedPartnerInfoInput
) {
    @Field(() => ID)
    @IsString()
    id: string
}

import {
    Injectable,
    NotFoundException,
    ConflictException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateUserInput } from './dto/create-user.input'
import { UpdateUserInput } from './dto/update-user.input'
import { admin } from '../auth/firebase-admin.module'
import { EmailHandlerService } from '../email-handler/email-handler.service'
import { AuthService } from '../auth/auth.service'

@Injectable()
export class UserService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly emailHandlerService: EmailHandlerService,
        private readonly authService: AuthService
    ) {}

    async create(createUserInput: CreateUserInput) {
        const existingUserByEmail = await this.prisma.user.findUnique({
            where: { email: createUserInput.email },
        })

        if (existingUserByEmail) {
            throw new ConflictException(
                `User with email ${createUserInput.email} already exists`
            )
        }

        const firebaseUser = await this.createFirebaseUser(
            createUserInput.email
        )

        const newUser = await this.prisma.user.create({
            data: { ...createUserInput, firebaseUid: firebaseUser.uid },
            include: {
                role: true,
            },
        })

        //set Claims
        await this.authService.setUserClaims({ firebaseUid: firebaseUser.uid })

        const resetLink = await this.authService.createResetLink(newUser.email)

        await this.emailHandlerService.sendNewUserEmail({
            email: newUser.email,
            resetLink: resetLink,
        })

        return newUser
    }

    createFirebaseUser = async (email: string) => {
        const password = Array(12)
            .fill(0)
            .map(() => String.fromCharCode(Math.floor(Math.random() * 94) + 33))
            .join('')
        try {
            const userCredential = await admin.auth().createUser({
                email,
                password,
            })

            await admin.auth().generatePasswordResetLink(email)
            return {
                uid: userCredential.uid,
                email: email,
                password: password,
            }
        } catch (error) {
            console.error('Error creating Firebase user:', error.message)
            throw new Error('Error creating Firebase user')
        }
    }

    async findAll() {
        return this.prisma.user.findMany({
            include: {
                role: true,
            },
        })
    }

    async findOne(id: string) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            include: {
                role: true,
            },
        })

        if (!user) {
            throw new NotFoundException(`User with ID ${id} not found`)
        }

        return user
    }

    async findByEmail(email: string) {
        const user = await this.prisma.user.findUnique({
            where: { email },
            include: {
                role: true,
            },
        })

        if (!user) {
            throw new NotFoundException(`User with email ${email} not found`)
        }

        return user
    }

    async findByFirebaseUid(firebaseUid: string) {
        const user = await this.prisma.user.findUnique({
            where: { firebaseUid },
            include: {
                role: true,
            },
        })

        if (!user) {
            throw new NotFoundException(
                `User with Firebase UID ${firebaseUid} not found`
            )
        }

        return user
    }

    async update(id: string, updateUserInput: UpdateUserInput) {
        const currentUser = await this.findOne(id)

        // Check if email is being updated and if it already exists for another user
        if (updateUserInput.email) {
            const existingUser = await this.prisma.user.findUnique({
                where: { email: updateUserInput.email },
            })

            if (existingUser && existingUser.id !== id) {
                throw new ConflictException(
                    `User with email ${updateUserInput.email} already exists`
                )
            }
        }

        // Check if role is being changed
        const isRoleChanged =
            updateUserInput.roleId &&
            updateUserInput.roleId !== currentUser.roleId

        const updatedUser = await this.prisma.user.update({
            where: { id },
            data: {
                ...updateUserInput,
                updatedAt: new Date(),
            },
            include: {
                role: true,
            },
        })

        // If role was changed, update user claims in Firebase
        if (isRoleChanged) {
            await this.authService.setUserClaims({
                firebaseUid: updatedUser.firebaseUid,
            })
        }

        return updatedUser
    }

    async updateLastLogin(id: string) {
        await this.findOne(id)

        return this.prisma.user.update({
            where: { id },
            data: {
                lastLogin: new Date(),
                updatedAt: new Date(),
            },
            include: {
                role: true,
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.user.delete({
            where: { id },
            include: {
                role: true,
            },
        })
    }
}

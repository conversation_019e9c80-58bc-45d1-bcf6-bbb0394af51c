import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { NotificationService } from '../notifications/notifications.service'
import { UserService } from '../user/user.service'
import { PusherService } from '../pusher/pusher.service'

export interface RejectFieldInput {
    entityId: string
    entityType: string
    fieldName: string
    rejectReason: string
    userId: string
}

export interface RejectFieldResponse {
    success: boolean
    entityId: string
    entityType: string
    fieldName: string
    message: string
}

@Injectable()
export class GenericRejectionService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly notificationService: NotificationService,
        private readonly userService: UserService,
        private readonly pusherService: PusherService
    ) {}

    async rejectField(input: RejectFieldInput): Promise<RejectFieldResponse> {
        const { entityId, entityType, fieldName, rejectReason, userId } = input

        // Map of entity types to their Prisma model names
        const entityModelMap: Record<string, string> = {
            // Non-certified entities
            personalInfo: 'personalInfo',
            partnerInfo: 'partnerInfo',
            address: 'address',
            child: 'child',
            employmentInfo: 'employmentInfo',
            pensionInfo: 'pensionInfo',
            salaryEntry: 'salaryEntry',
            // Certified entities (for compatibility)
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedAddress: 'certifiedAddress',
            certifiedChild: 'certifiedChild',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
            certifiedSalaryEntry: 'certifiedSalaryEntry',
        }

        // Map of entity types to their foreign key field names in CertificationRejectReason
        const foreignKeyMap: Record<string, string> = {
            // Non-certified entities
            personalInfo: 'personalInfoId',
            partnerInfo: 'partnerInfoId',
            address: 'addressId',
            child: 'childId',
            employmentInfo: 'employmentInfoId',
            pensionInfo: 'pensionInfoId',
            salaryEntry: 'salaryEntryId',
            // Certified entities (for compatibility)
            certifiedData: 'certifiedDataId',
            certifiedPensionInfo: 'certifiedPensionInfoId',
            certifiedEmploymentInfo: 'certifiedEmploymentInfoId',
            certifiedPersonalInfo: 'certifiedPersonalInfoId',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYearId',
            certifiedPensionCorrections: 'certifiedPensionCorrectionsId',
            certifiedVoluntaryContributions:
                'certifiedVoluntaryContributionsId',
            certifiedPensionParameters: 'certifiedPensionParametersId',
            certifiedAddress: 'certifiedAddressId',
            certifiedChild: 'certifiedChildId',
            certifiedPartnerInfo: 'certifiedPartnerInfoId',
            certifiedSalaryEntry: 'certifiedSalaryEntryId',
        }

        const modelName = entityModelMap[entityType]
        if (!modelName) {
            throw new BadRequestException(
                `Unsupported entity type: ${entityType}`
            )
        }

        const foreignKeyField = foreignKeyMap[entityType]
        if (!foreignKeyField) {
            throw new BadRequestException(
                `No foreign key mapping for entity type: ${entityType}`
            )
        }

        // Find the entity
        const entity = await this.prisma[modelName].findUnique({
            where: { id: entityId },
        })

        if (!entity) {
            throw new NotFoundException(
                `${entityType} with ID ${entityId} not found`
            )
        }

        await this.prisma.$transaction(async (prisma) => {
            // Add the field to pendingChanges if it's not already there
            const currentPendingChanges = entity.pendingChanges || []
            const updatedPendingChanges = currentPendingChanges.includes(
                fieldName
            )
                ? currentPendingChanges
                : [...currentPendingChanges, fieldName]

            // Update the entity with the pending changes
            await prisma[modelName].update({
                where: { id: entityId },
                data: {
                    pendingChanges: updatedPendingChanges,
                },
            })

            // Create the reject reason
            await prisma.certificationRejectReason.create({
                data: {
                    field: fieldName,
                    reason: rejectReason,
                    status: 'VALID',
                    [foreignKeyField]: entityId,
                },
            })
        })

        await this.notifyReviewersAndAdmins(
            entityId,
            entityType,
            fieldName,
            userId
        )
        await this.sendPusherEvent(entityId, entityType, fieldName)

        return {
            success: true,
            entityId,
            entityType,
            fieldName,
            message: `Field "${fieldName}" rejected successfully for ${entityType}`,
        }
    }

    private async notifyReviewersAndAdmins(
        entityId: string,
        entityType: string,
        fieldName: string,
        currentUserId: string
    ): Promise<void> {
        try {
            const users = await this.userService.findAll()
            const reviewersAndAdmins = users.filter(
                (user) =>
                    user.role &&
                    (user.role.name.toLowerCase() === 'reviewer' ||
                        user.role.name.toLowerCase() === 'admin')
            )

            console.log({ reviewersAndAdmins })

            // Send notification to each reviewer/admin
            const notificationPromises = reviewersAndAdmins.map((user) =>
                this.notificationService.create(
                    {
                        message: `A field has been rejected and requires attention. Entity: ${entityType}, Field: ${fieldName}`,
                        recipientId: user.id,
                        type: 'FIELD_REJECTED',
                        entityId: entityId,
                        entityType: entityType,
                    },
                    currentUserId
                )
            )

            await Promise.all(notificationPromises)
        } catch (error) {
            console.error('Failed to notify reviewers and admins:', error)
        }
    }

    private async sendPusherEvent(
        entityId: string,
        entityType: string,
        fieldName: string
    ): Promise<void> {
        try {
            // Get the same users that we notify (reviewers and admins)
            const users = await this.userService.findAll()
            const reviewersAndAdmins = users.filter(
                (user) =>
                    user.role &&
                    (user.role.name.toLowerCase() === 'reviewer' ||
                        user.role.name.toLowerCase() === 'admin')
            )

            // Send Pusher event to each reviewer/admin user channel
            const pusherPromises = reviewersAndAdmins.map((user) =>
                this.pusherService.publishToUserChannel(
                    user.id,
                    'field-rejected',
                    {
                        entityId,
                        entityType,
                        fieldName,
                    }
                )
            )

            await Promise.all(pusherPromises)
        } catch (error) {
            console.error('Failed to send Pusher event:', error)
        }
    }
}

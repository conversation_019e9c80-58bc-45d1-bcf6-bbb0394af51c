import { Module } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { GenericRejectionService } from './generic-rejection.service'
import { GenericRejectionResolver } from './generic-rejection.resolver'
import { AuthModule } from '../auth/auth.module'
import { UserModule } from '../user/user.module'
import { NotificationsModule } from '../notifications/notifications.module'
import { PusherModule } from '../pusher/pusher.module'

@Module({
    imports: [AuthModule, UserModule, NotificationsModule, PusherModule],
    providers: [
        PrismaService,
        GenericRejectionService,
        GenericRejectionResolver,
    ],
    exports: [GenericRejectionService],
})
export class CommonModule {}

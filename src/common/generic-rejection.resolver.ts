import { Resolver, Mutation, Args, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { GenericRejectionService } from './generic-rejection.service'
import { RejectFieldInput, RejectFieldResponse } from './dto/reject-field.input'

@Resolver()
export class GenericRejectionResolver {
    constructor(
        private readonly genericRejectionService: GenericRejectionService
    ) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => RejectFieldResponse)
    async rejectField(
        @Args('input') input: RejectFieldInput,
        @Context() context: any
    ): Promise<RejectFieldResponse> {
        const userId =
            context?.req?.user?.pensionUserId ||
            context?.user?.pensionUserId ||
            input.userId

        return await this.genericRejectionService.rejectField({
            ...input,
            userId,
        })
    }
}

import { InputType, ObjectType, Field, ID } from '@nestjs/graphql'
import { IsString, IsNotEmpty, IsUUID } from 'class-validator'

@InputType()
export class RejectFieldInput {
    @Field(() => ID)
    @IsUUID('4')
    @IsNotEmpty()
    entityId: string

    @Field()
    @IsString()
    @IsNotEmpty()
    entityType: string

    @Field()
    @IsString()
    @IsNotEmpty()
    fieldName: string

    @Field()
    @IsString()
    @IsNotEmpty()
    rejectReason: string

    @Field(() => ID)
    @IsUUID('4')
    @IsNotEmpty()
    userId: string
}

@ObjectType()
export class RejectFieldResponse {
    @Field()
    success: boolean

    @Field(() => ID)
    entityId: string

    @Field()
    entityType: string

    @Field()
    fieldName: string

    @Field()
    message: string
}

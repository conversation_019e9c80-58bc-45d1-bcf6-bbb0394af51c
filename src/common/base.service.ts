import { toPrismaDate } from '../utils/date.utils'

export class BaseService {
    protected handleDateFields(path: string, value: any): any {
        const dateFields = [
            'dateOfBirth',
            'startDate',
            'endDate',
            'effectiveDate',
            'retirementDate',
            'certifiedAt',
            'createdAt',
            'updatedAt',
            'reviewedAt',
        ]

        return dateFields.includes(path) ? toPrismaDate(value) : value
    }
}

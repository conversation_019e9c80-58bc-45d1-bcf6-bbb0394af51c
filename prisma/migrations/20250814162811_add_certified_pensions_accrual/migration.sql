-- AlterTable
ALTER TABLE "CertificationRejectReason" ADD COLUMN     "certifiedPensionsAccrualId" UUID;

-- CreateTable
CREATE TABLE "CertifiedPensionsAccrual" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "pensionBase" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionsAccrual_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionsAccrual_certifiedDataId_key" ON "CertifiedPensionsAccrual"("certifiedDataId");

-- AddForeignKey
ALTER TABLE "CertifiedPensionsAccrual" ADD CONSTRAINT "CertifiedPensionsAccrual_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionsAccrualId_fkey" FOREIGN KEY ("certifiedPensionsAccrualId") REFERENCES "CertifiedPensionsAccrual"("id") ON DELETE SET NULL ON UPDATE CASCADE;

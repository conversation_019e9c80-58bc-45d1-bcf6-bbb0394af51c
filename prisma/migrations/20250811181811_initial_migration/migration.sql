-- CreateEnum
CREATE TYPE "ChangeStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "ApprovalStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "ChangeType" AS ENUM ('PARTICIPANT', 'PARAMETERS', 'SALARY', 'CERTIFIED_DATA');

-- CreateEnum
CREATE TYPE "CertificationRejectReasonStatus" AS ENUM ('VALID', 'INVALID');

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "firebaseUid" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "email" TEXT NOT NULL,
    "firstname" TEXT,
    "lastname" TEXT,
    "lastLogin" TIMESTAMP(3),
    "roleId" UUID NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionCorrections" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "correction" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "year" TEXT NOT NULL,
    "reviewedAt" TIMESTAMP(3) NOT NULL,
    "createdById" UUID NOT NULL,
    "reviewedById" UUID NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PensionCorrections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SystemSettings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "autoApproveChanges" BOOLEAN NOT NULL DEFAULT false,
    "effectiveDate" TIMESTAMP(3) NOT NULL,
    "passwordExpiryDays" INTEGER NOT NULL DEFAULT 90,
    "requireTwoFactorAuth" BOOLEAN NOT NULL DEFAULT true,
    "sessionTimeout" INTEGER NOT NULL DEFAULT 30,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID NOT NULL,

    CONSTRAINT "SystemSettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionParameters" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "accrualPercentage" DOUBLE PRECISION NOT NULL DEFAULT 0.02,
    "annualMultiplier" DOUBLE PRECISION NOT NULL DEFAULT 13,
    "offsetAmount" DOUBLE PRECISION NOT NULL DEFAULT 17616,
    "partnersPensionPercentage" DOUBLE PRECISION NOT NULL DEFAULT 0.70,
    "retirementAge" INTEGER NOT NULL DEFAULT 65,
    "voluntaryContributionInterestRate" DOUBLE PRECISION NOT NULL DEFAULT 0.04,
    "minimumPensionBase" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "year" TEXT NOT NULL,
    "effectiveDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" UUID NOT NULL,
    "status" TEXT DEFAULT 'pending',
    "pendingChanges" TEXT[],
    "rejectReason" TEXT,

    CONSTRAINT "PensionParameters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeProposal" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" UUID NOT NULL,
    "effectiveDate" TIMESTAMP(3) NOT NULL,
    "entityId" TEXT,
    "entityType" TEXT NOT NULL,
    "type" "ChangeType" DEFAULT 'PARTICIPANT',
    "participantName" TEXT,
    "status" "ChangeStatus" NOT NULL DEFAULT 'PENDING',
    "reviewComments" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "reviewedById" UUID,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isCertificationProposal" BOOLEAN NOT NULL DEFAULT false,
    "changePropagated" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ChangeProposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeData" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "changeProposalId" UUID NOT NULL,
    "path" TEXT NOT NULL,
    "newValue" TEXT NOT NULL,
    "oldValue" TEXT,

    CONSTRAINT "ChangeData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Participant" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "approvalStatus" TEXT NOT NULL DEFAULT 'PENDING',
    "rejectReason" TEXT,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedBy" TEXT NOT NULL,
    "lastModified" TIMESTAMP(3),

    CONSTRAINT "Participant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PersonalInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "sex" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "maritalStatus" TEXT,
    "birthDay" INTEGER,
    "birthMonth" INTEGER,
    "birthYear" INTEGER,
    "pendingChanges" TEXT[],

    CONSTRAINT "PersonalInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Address" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "street" TEXT,
    "houseNumber" TEXT,
    "postalCode" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "pendingChanges" TEXT[],

    CONSTRAINT "Address_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PartnerInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "isCurrent" BOOLEAN NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isDeceased" BOOLEAN NOT NULL DEFAULT false,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "code" INTEGER,
    "previousCode" INTEGER,
    "codeDescription" TEXT,
    "pendingChanges" TEXT[],

    CONSTRAINT "PartnerInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Child" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "personalInfoId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isOrphan" BOOLEAN NOT NULL DEFAULT false,
    "isStudying" BOOLEAN NOT NULL DEFAULT false,
    "code" INTEGER,
    "previousCode" INTEGER,
    "codeDescription" TEXT,
    "pendingChanges" TEXT[],

    CONSTRAINT "Child_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "documentId" TEXT,
    "name" TEXT,
    "type" TEXT,
    "mimeType" TEXT,
    "path" TEXT,
    "size" INTEGER,
    "createdBy" TEXT,
    "uploadedAt" TIMESTAMP(3),

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmploymentInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "employeeId" TEXT,
    "department" TEXT,
    "position" TEXT,
    "regNum" INTEGER,
    "havNum" INTEGER,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "status" TEXT,
    "pendingChanges" TEXT[],

    CONSTRAINT "EmploymentInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryEntry" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "employmentInfoId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "partTimePercentage" DOUBLE PRECISION NOT NULL,
    "pendingChanges" TEXT[],

    CONSTRAINT "SalaryEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PensionInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "code" INTEGER,
    "previousCode" INTEGER,
    "codeEffectiveDate" TIMESTAMP(3),
    "previousCodeEffectiveDate" TIMESTAMP(3),
    "codeDescription" TEXT,
    "codeImpact" TEXT,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "pensionBase" DOUBLE PRECISION,
    "pendingChanges" TEXT[],

    CONSTRAINT "PensionInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "action" TEXT NOT NULL,
    "userId" UUID NOT NULL,
    "userRole" TEXT,
    "entityId" UUID NOT NULL,
    "entityType" TEXT NOT NULL,
    "proposalId" UUID,
    "changeDataId" UUID NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedData" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "participantId" UUID NOT NULL,
    "certificationYear" INTEGER NOT NULL,
    "certificationStatus" TEXT,
    "certifiedAt" TIMESTAMP(3) NOT NULL,
    "certifiedById" UUID NOT NULL,
    "notes" TEXT,

    CONSTRAINT "CertifiedData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "code" INTEGER,
    "codeDescription" TEXT,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "pensionBase" DOUBLE PRECISION,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedSalaryEntry" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedEmploymentInfoId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "partTimePercentage" DOUBLE PRECISION NOT NULL,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedSalaryEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedEmploymentInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "employeeId" TEXT,
    "department" TEXT,
    "position" TEXT,
    "regNum" INTEGER,
    "havNum" INTEGER,
    "startDate" TIMESTAMP(3),
    "status" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedEmploymentInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPersonalInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "sex" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "maritalStatus" TEXT,
    "birthDay" INTEGER,
    "birthMonth" INTEGER,
    "birthYear" INTEGER,
    "address" JSONB,
    "partnerInfo" JSONB,
    "children" JSONB,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPersonalInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPartnerInfo" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isDeceased" BOOLEAN NOT NULL DEFAULT false,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isCurrent" BOOLEAN NOT NULL,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPartnerInfo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedChild" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "isOrphan" BOOLEAN NOT NULL DEFAULT false,
    "isStudying" BOOLEAN NOT NULL DEFAULT false,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedChild_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedIndexationStartOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedIndexationStartOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionCorrections" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionCorrections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionCorrectionsStartOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionCorrectionsStartOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionCorrectionsEndOfYear" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "attainableGrossAnnualOldAgePension" DOUBLE PRECISION,
    "accruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "accruedGrossAnnualSinglesPension" DOUBLE PRECISION,
    "grossAnnualDisabilityPension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualOldAgePension" DOUBLE PRECISION,
    "extraAccruedGrossAnnualPartnersPension" DOUBLE PRECISION,
    "correction" DOUBLE PRECISION,
    "year" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionCorrectionsEndOfYear_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedVoluntaryContributions" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "contributions" JSONB,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedVoluntaryContributions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedPensionParameters" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "accrualPercentage" DOUBLE PRECISION,
    "annualMultiplier" DOUBLE PRECISION,
    "offsetAmount" DOUBLE PRECISION,
    "partnersPensionPercentage" DOUBLE PRECISION,
    "retirementAge" INTEGER,
    "voluntaryContributionInterestRate" DOUBLE PRECISION,
    "minimumPensionBase" DOUBLE PRECISION,
    "year" TEXT,
    "effectiveDate" TIMESTAMP(3),
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedPensionParameters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertifiedAddress" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "certifiedDataId" UUID NOT NULL,
    "street" TEXT,
    "houseNumber" TEXT,
    "postalCode" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "pendingChanges" TEXT[],
    "requestedChanges" TEXT[],
    "approvedChanges" TEXT[],

    CONSTRAINT "CertifiedAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Role" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "pendingChanges" TEXT[],

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" UUID NOT NULL,
    "message" TEXT NOT NULL,
    "recipientId" UUID NOT NULL,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "type" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CertificationRejectReason" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "field" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "status" "CertificationRejectReasonStatus" NOT NULL DEFAULT 'VALID',
    "submittedForReview" BOOLEAN NOT NULL DEFAULT false,
    "submittedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "certifiedDataId" UUID,
    "certifiedPensionInfoId" UUID,
    "certifiedSalaryEntryId" UUID,
    "certifiedEmploymentInfoId" UUID,
    "certifiedPersonalInfoId" UUID,
    "certifiedChildId" UUID,
    "certifiedIndexationStartOfYearId" UUID,
    "certifiedPensionCorrectionsId" UUID,
    "certifiedVoluntaryContributionsId" UUID,
    "certifiedPensionParametersId" UUID,
    "certifiedPartnerInfoId" UUID,
    "certifiedAddressId" UUID,
    "personalInfoId" UUID,
    "certifiedPensionCorrectionsStartOfYearId" UUID,
    "certifiedPensionCorrectionsEndOfYearId" UUID,
    "partnerInfoId" UUID,
    "addressId" UUID,
    "childId" UUID,
    "employmentInfoId" UUID,
    "pensionInfoId" UUID,
    "salaryEntryId" UUID,

    CONSTRAINT "CertificationRejectReason_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_firebaseUid_key" ON "User"("firebaseUid");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "ChangeData_changeProposalId_idx" ON "ChangeData"("changeProposalId");

-- CreateIndex
CREATE UNIQUE INDEX "PersonalInfo_participantId_key" ON "PersonalInfo"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "Address_personalInfoId_key" ON "Address"("personalInfoId");

-- CreateIndex
CREATE INDEX "Child_personalInfoId_idx" ON "Child"("personalInfoId");

-- CreateIndex
CREATE INDEX "Document_participantId_idx" ON "Document"("participantId");

-- CreateIndex
CREATE UNIQUE INDEX "EmploymentInfo_participantId_key" ON "EmploymentInfo"("participantId");

-- CreateIndex
CREATE INDEX "SalaryEntry_employmentInfoId_idx" ON "SalaryEntry"("employmentInfoId");

-- CreateIndex
CREATE UNIQUE INDEX "SalaryEntry_employmentInfoId_year_key" ON "SalaryEntry"("employmentInfoId", "year");

-- CreateIndex
CREATE UNIQUE INDEX "PensionInfo_participantId_key" ON "PensionInfo"("participantId");

-- CreateIndex
CREATE INDEX "AuditLog_timestamp_idx" ON "AuditLog"("timestamp");

-- CreateIndex
CREATE INDEX "AuditLog_userId_idx" ON "AuditLog"("userId");

-- CreateIndex
CREATE INDEX "AuditLog_entityId_entityType_idx" ON "AuditLog"("entityId", "entityType");

-- CreateIndex
CREATE INDEX "AuditLog_proposalId_idx" ON "AuditLog"("proposalId");

-- CreateIndex
CREATE INDEX "CertifiedData_participantId_idx" ON "CertifiedData"("participantId");

-- CreateIndex
CREATE INDEX "CertifiedData_certificationYear_idx" ON "CertifiedData"("certificationYear");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedData_participantId_certificationYear_key" ON "CertifiedData"("participantId", "certificationYear");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionInfo_certifiedDataId_key" ON "CertifiedPensionInfo"("certifiedDataId");

-- CreateIndex
CREATE INDEX "CertifiedSalaryEntry_certifiedEmploymentInfoId_idx" ON "CertifiedSalaryEntry"("certifiedEmploymentInfoId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedSalaryEntry_certifiedEmploymentInfoId_year_key" ON "CertifiedSalaryEntry"("certifiedEmploymentInfoId", "year");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedEmploymentInfo_certifiedDataId_key" ON "CertifiedEmploymentInfo"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPersonalInfo_certifiedDataId_key" ON "CertifiedPersonalInfo"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedIndexationStartOfYear_certifiedDataId_key" ON "CertifiedIndexationStartOfYear"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrections_certifiedDataId_key" ON "CertifiedPensionCorrections"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrectionsStartOfYear_certifiedDataId_key" ON "CertifiedPensionCorrectionsStartOfYear"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionCorrectionsEndOfYear_certifiedDataId_key" ON "CertifiedPensionCorrectionsEndOfYear"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedVoluntaryContributions_certifiedDataId_key" ON "CertifiedVoluntaryContributions"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedPensionParameters_certifiedDataId_key" ON "CertifiedPensionParameters"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "CertifiedAddress_certifiedDataId_key" ON "CertifiedAddress"("certifiedDataId");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_key" ON "Role"("name");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionCorrections" ADD CONSTRAINT "PensionCorrections_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionCorrections" ADD CONSTRAINT "PensionCorrections_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SystemSettings" ADD CONSTRAINT "SystemSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionParameters" ADD CONSTRAINT "PensionParameters_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeProposal" ADD CONSTRAINT "ChangeProposal_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeProposal" ADD CONSTRAINT "ChangeProposal_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeData" ADD CONSTRAINT "ChangeData_changeProposalId_fkey" FOREIGN KEY ("changeProposalId") REFERENCES "ChangeProposal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonalInfo" ADD CONSTRAINT "PersonalInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PartnerInfo" ADD CONSTRAINT "PartnerInfo_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Child" ADD CONSTRAINT "Child_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmploymentInfo" ADD CONSTRAINT "EmploymentInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryEntry" ADD CONSTRAINT "SalaryEntry_employmentInfoId_fkey" FOREIGN KEY ("employmentInfoId") REFERENCES "EmploymentInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PensionInfo" ADD CONSTRAINT "PensionInfo_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_changeDataId_fkey" FOREIGN KEY ("changeDataId") REFERENCES "ChangeData"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedData" ADD CONSTRAINT "CertifiedData_participantId_fkey" FOREIGN KEY ("participantId") REFERENCES "Participant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedData" ADD CONSTRAINT "CertifiedData_certifiedById_fkey" FOREIGN KEY ("certifiedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionInfo" ADD CONSTRAINT "CertifiedPensionInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedSalaryEntry" ADD CONSTRAINT "CertifiedSalaryEntry_certifiedEmploymentInfoId_fkey" FOREIGN KEY ("certifiedEmploymentInfoId") REFERENCES "CertifiedEmploymentInfo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedEmploymentInfo" ADD CONSTRAINT "CertifiedEmploymentInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPersonalInfo" ADD CONSTRAINT "CertifiedPersonalInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPartnerInfo" ADD CONSTRAINT "CertifiedPartnerInfo_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedChild" ADD CONSTRAINT "CertifiedChild_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedIndexationStartOfYear" ADD CONSTRAINT "CertifiedIndexationStartOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrections" ADD CONSTRAINT "CertifiedPensionCorrections_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrectionsStartOfYear" ADD CONSTRAINT "CertifiedPensionCorrectionsStartOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionCorrectionsEndOfYear" ADD CONSTRAINT "CertifiedPensionCorrectionsEndOfYear_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedVoluntaryContributions" ADD CONSTRAINT "CertifiedVoluntaryContributions_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedPensionParameters" ADD CONSTRAINT "CertifiedPensionParameters_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertifiedAddress" ADD CONSTRAINT "CertifiedAddress_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_recipientId_fkey" FOREIGN KEY ("recipientId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedDataId_fkey" FOREIGN KEY ("certifiedDataId") REFERENCES "CertifiedData"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionInfoId_fkey" FOREIGN KEY ("certifiedPensionInfoId") REFERENCES "CertifiedPensionInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedSalaryEntryId_fkey" FOREIGN KEY ("certifiedSalaryEntryId") REFERENCES "CertifiedSalaryEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedEmploymentInfoId_fkey" FOREIGN KEY ("certifiedEmploymentInfoId") REFERENCES "CertifiedEmploymentInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPersonalInfoId_fkey" FOREIGN KEY ("certifiedPersonalInfoId") REFERENCES "CertifiedPersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedChildId_fkey" FOREIGN KEY ("certifiedChildId") REFERENCES "CertifiedChild"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedIndexationStartOfYearId_fkey" FOREIGN KEY ("certifiedIndexationStartOfYearId") REFERENCES "CertifiedIndexationStartOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsId_fkey" FOREIGN KEY ("certifiedPensionCorrectionsId") REFERENCES "CertifiedPensionCorrections"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedVoluntaryContributionsI_fkey" FOREIGN KEY ("certifiedVoluntaryContributionsId") REFERENCES "CertifiedVoluntaryContributions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionParametersId_fkey" FOREIGN KEY ("certifiedPensionParametersId") REFERENCES "CertifiedPensionParameters"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPartnerInfoId_fkey" FOREIGN KEY ("certifiedPartnerInfoId") REFERENCES "CertifiedPartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedAddressId_fkey" FOREIGN KEY ("certifiedAddressId") REFERENCES "CertifiedAddress"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_personalInfoId_fkey" FOREIGN KEY ("personalInfoId") REFERENCES "PersonalInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsStart_fkey" FOREIGN KEY ("certifiedPensionCorrectionsStartOfYearId") REFERENCES "CertifiedPensionCorrectionsStartOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_certifiedPensionCorrectionsEndOf_fkey" FOREIGN KEY ("certifiedPensionCorrectionsEndOfYearId") REFERENCES "CertifiedPensionCorrectionsEndOfYear"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_partnerInfoId_fkey" FOREIGN KEY ("partnerInfoId") REFERENCES "PartnerInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_childId_fkey" FOREIGN KEY ("childId") REFERENCES "Child"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_employmentInfoId_fkey" FOREIGN KEY ("employmentInfoId") REFERENCES "EmploymentInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_pensionInfoId_fkey" FOREIGN KEY ("pensionInfoId") REFERENCES "PensionInfo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CertificationRejectReason" ADD CONSTRAINT "CertificationRejectReason_salaryEntryId_fkey" FOREIGN KEY ("salaryEntryId") REFERENCES "SalaryEntry"("id") ON DELETE SET NULL ON UPDATE CASCADE;
